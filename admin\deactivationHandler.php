<?php
/**
 * Handles plugin deactivation confirmation and data export
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Deactivation Handler class
 */
class Custom_Linking_Deactivation_Handler {
    
    /**
     * Initialize deactivation handler
     */
    public static function init() {
        // Add AJAX handlers for deactivation process
        add_action('wp_ajax_custom_linking_export_data', array(__CLASS__, 'handle_export_request'));
        add_action('wp_ajax_custom_linking_get_data_count', array(__CLASS__, 'handle_data_count_request'));
        
        // Add deactivation confirmation script to plugins page
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_deactivation_scripts'));
    }
    
    /**
     * Enqueue deactivation confirmation scripts
     */
    public static function enqueue_deactivation_scripts($hook) {
        // Only load on plugins page
        if ($hook !== 'plugins.php') {
            return;
        }
        
        // Enqueue the deactivation script
        wp_enqueue_script(
            'custom-linking-deactivation',
            plugins_url('admin/js/deactivation.js', dirname(__FILE__)),
            array('jquery'),
            '1.1.0',
            true
        );
        
        // Enqueue styles
        wp_enqueue_style(
            'custom-linking-deactivation-style',
            plugins_url('admin/css/deactivation.css', dirname(__FILE__)),
            array(),
            '1.1.0'
        );
        
        // Localize script with AJAX data
        wp_localize_script('custom-linking-deactivation', 'customLinkingDeactivation', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('custom_linking_deactivation_nonce'),
            'pluginFile' => 'Vedmg-woo-LMS/custom-linking-plugin.php', // Adjust to your plugin's main file
            'strings' => array(
                'confirmTitle' => __('Confirm Plugin Deactivation', 'custom-linking-plugin'),
                'confirmMessage' => __('This will permanently delete all course-product links and plugin settings.', 'custom-linking-plugin'),
                'exportOption' => __('Export data before deactivation?', 'custom-linking-plugin'),
                'exportYes' => __('Yes, export my data first', 'custom-linking-plugin'),
                'exportNo' => __('No, just delete everything', 'custom-linking-plugin'),
                'deactivateButton' => __('Proceed with Deactivation', 'custom-linking-plugin'),
                'cancelButton' => __('Cancel', 'custom-linking-plugin'),
                'exportingMessage' => __('Exporting data...', 'custom-linking-plugin'),
                'deactivatingMessage' => __('Deactivating plugin...', 'custom-linking-plugin'),
                'exportSuccess' => __('Data exported successfully!', 'custom-linking-plugin'),
                'exportFailed' => __('Export failed. Proceed anyway?', 'custom-linking-plugin'),
                'noDataFound' => __('No data found to export.', 'custom-linking-plugin'),
                'dataCount' => __('records will be permanently deleted.', 'custom-linking-plugin')
            )
        ));
    }
    
    /**
     * Handle AJAX export request
     */
    public static function handle_export_request() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'custom_linking_deactivation_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check user capabilities
        if (!current_user_can('activate_plugins')) {
            wp_die('Insufficient permissions');
        }
        
        // Include the Deactivator class
        require_once dirname(dirname(__FILE__)) . '/database/Deactivator.php';
        
        // Export data
        $result = Custom_Linking_Deactivator::export_data_to_csv();
        
        // Return JSON response
        wp_send_json($result);
    }
    
    /**
     * Handle AJAX data count request
     */
    public static function handle_data_count_request() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'custom_linking_deactivation_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check user capabilities
        if (!current_user_can('activate_plugins')) {
            wp_die('Insufficient permissions');
        }
        
        // Include the Deactivator class
        require_once dirname(dirname(__FILE__)) . '/database/Deactivator.php';
        
        // Get data count
        $count = Custom_Linking_Deactivator::get_data_count();
        
        // Return JSON response
        wp_send_json_success(array('count' => $count));
    }
}

// Initialize the deactivation handler
Custom_Linking_Deactivation_Handler::init();
