<?php
/**
 * WooCommerce Order Completion Handler for Custom Linking Plugin
 * 
 * This file handles WooCommerce order completion events and automatically
 * grants course access to users who purchase linked products
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

/**
 * Initialize WooCommerce order completion hooks
 * 
 * This function sets up hooks for both processing and completed order statuses
 * to handle different payment methods (offline payments go to processing)
 */
function custom_linking_init_order_hooks() {
    // Hook into order status changes for both processing and completed
    add_action('woocommerce_order_status_processing', 'custom_linking_handle_order_completion');
    add_action('woocommerce_order_status_completed', 'custom_linking_handle_order_completion');
    
    // Also hook into order payment complete for immediate processing
    add_action('woocommerce_payment_complete', 'custom_linking_handle_order_completion');
}

/**
 * Handle WooCommerce order completion
 *
 * This function processes completed/processing orders and grants course access
 * for any linked products in the order
 *
 * @param int $order_id The WooCommerce order ID
 */
function custom_linking_handle_order_completion($order_id) {
    try {
        // Validate order ID
        if (!$order_id) {
            custom_linking_debug_log('Order completion: No order ID provided');
            return;
        }
        
        // ORDER PROCESSING GUARD: Prevent duplicate processing of the same order
        $order_meta_key = '_custom_linking_processed';
        $already_processed = get_post_meta($order_id, $order_meta_key, true);
        
        if ($already_processed) {
            custom_linking_debug_log("Order {$order_id} already processed - skipping to prevent duplicates");
            return;
        }

        // Check if WooCommerce is available
        if (!function_exists('wc_get_order')) {
            custom_linking_debug_log('Order completion: WooCommerce functions not available');
            return;
        }

        // Get the WooCommerce order object
        $order = wc_get_order($order_id);
        if (!$order) {
            custom_linking_debug_log("Order completion: Could not retrieve order {$order_id}");
            return;
        }

        // Get the user who made the purchase
        $user_id = $order->get_user_id();
        if (!$user_id) {
            custom_linking_debug_log("Order completion: No user ID found for order {$order_id}");
            return;
        }

        // Log the order processing start
        custom_linking_debug_log("Processing order {$order_id} for user {$user_id}");

        // Get all items in the order
        $order_items = $order->get_items();
        if (empty($order_items)) {
            custom_linking_debug_log("Order completion: No items found in order {$order_id}");
            return;
        }
        
        // DETAILED ORDER ITEM ANALYSIS: Log every item in the order
        custom_linking_debug_log("=== DETAILED ORDER ITEM ANALYSIS ===");
        custom_linking_debug_log("Order {$order_id} contains " . count($order_items) . " items:");
        foreach ($order_items as $item_id => $item) {
            $product_id = $item->get_product_id();
            $product_name = $item->get_name();
            $product_type = '';
            $item_meta = $item->get_meta_data();
            
            if ($product_id) {
                $product = wc_get_product($product_id);
                if ($product) {
                    $product_type = $product->get_type();
                }
            }
            
            custom_linking_debug_log("  Item {$item_id}: Product #{$product_id} '{$product_name}' (Type: {$product_type})");
            
            // Log item meta data to see if there are any clues
            if (!empty($item_meta)) {
                custom_linking_debug_log("    Item meta: " . print_r($item_meta, true));
            }
        }
        custom_linking_debug_log("=== END ORDER ITEM ANALYSIS ===");

        // FIRST PASS: Collect all products and their linked courses to prevent duplicates
        $all_products_in_order = array();
        $bundle_courses = array(); // Track courses that come from bundles
        $individual_courses = array(); // Track courses that come from individual products
        
        foreach ($order_items as $item_id => $item) {
            $product_id = $item->get_product_id();
            if ($product_id) {
                $linked_courses = custom_linking_get_courses_for_product($product_id);
                $all_products_in_order[$product_id] = $linked_courses;
                
                // Categorize courses by their source type
                foreach ($linked_courses as $course_link) {
                    if ($course_link->type === 'bundle') {
                        $bundle_courses[$course_link->course_id] = $product_id;
                    } else {
                        $individual_courses[$course_link->course_id] = $product_id;
                    }
                }
            }
        }
        
        custom_linking_debug_log("=== ORDER ANALYSIS ===");
        custom_linking_debug_log("Total products in order: " . count($all_products_in_order));
        custom_linking_debug_log("Bundle courses found: " . count($bundle_courses));
        custom_linking_debug_log("Individual courses found: " . count($individual_courses));
        custom_linking_debug_log("Bundle courses: " . print_r(array_keys($bundle_courses), true));
        custom_linking_debug_log("Individual courses: " . print_r(array_keys($individual_courses), true));
        
        // SECOND PASS: Process products with duplicate prevention
        $processed_courses = array(); // Track which courses have already been granted
        
        foreach ($order_items as $item_id => $item) {
            try {
                $product_id = $item->get_product_id();

                if ($product_id) {
                    custom_linking_debug_log("Processing product {$product_id} from order {$order_id}");
                    
                    // BUNDLE CHILD FILTER: Skip products that are children of bundle products
                    $item_meta = $item->get_meta_data();
                    $is_bundle_child = false;
                    $parent_bundle_id = null;
                    
                    foreach ($item_meta as $meta) {
                        if ($meta->key === '_asnp_wepb_parent_id') {
                            $is_bundle_child = true;
                            $parent_bundle_id = $meta->value;
                            break;
                        }
                    }
                    
                    if ($is_bundle_child) {
                        custom_linking_debug_log("BUNDLE CHILD DETECTED: Product {$product_id} is a child of bundle product {$parent_bundle_id} - skipping to prevent duplicate course access");
                        continue; // Skip this product entirely
                    }

                    // Find linked courses for this product
                    $linked_courses = custom_linking_get_courses_for_product($product_id);

                    if (!empty($linked_courses)) {
                        foreach ($linked_courses as $course_link) {
                            try {
                                $course_id = $course_link->course_id;
                                $link_type = $course_link->type;

                                custom_linking_debug_log("Found linked course {$course_id} (type: {$link_type}) for product {$product_id}");
                                
                                // DUPLICATE PREVENTION: Check if this course has already been processed
                                if (isset($processed_courses[$course_id])) {
                                    custom_linking_debug_log("DUPLICATE PREVENTION: Course {$course_id} already processed via product {$processed_courses[$course_id]} - skipping");
                                    continue;
                                }
                                
                                // BUNDLE PRIORITY: If this course is available via both bundle and individual product,
                                // prioritize the bundle and skip individual product processing
                                if ($link_type === 'course' && isset($bundle_courses[$course_id])) {
                                    custom_linking_debug_log("BUNDLE PRIORITY: Course {$course_id} is available via bundle (product {$bundle_courses[$course_id]}) - skipping individual product {$product_id}");
                                    continue;
                                }

                                // Grant course access to the user
                                custom_linking_debug_log("ATTEMPTING TO GRANT: Course {$course_id} access to user {$user_id} (type: {$link_type})");

                                $access_granted = custom_linking_grant_course_access($user_id, $course_id, $link_type);

                                if ($access_granted) {
                                    custom_linking_debug_log("SUCCESS: Granted access to course {$course_id} for user {$user_id}");
                                    $processed_courses[$course_id] = $product_id; // Mark as processed
                                } else {
                                    custom_linking_debug_log("FAILED: Could not grant access to course {$course_id} for user {$user_id}");
                                }
                            } catch (Exception $e) {
                                custom_linking_debug_log("Exception processing course link: " . $e->getMessage());
                            }
                        }
                    } else {
                        custom_linking_debug_log("No linked courses found for product {$product_id}");
                    }
                } else {
                    custom_linking_debug_log("No product ID found for item in order {$order_id}");
                }
            } catch (Exception $e) {
                custom_linking_debug_log("Exception processing order item: " . $e->getMessage());
            }
        }
        
        custom_linking_debug_log("=== ORDER PROCESSING SUMMARY ===");
        custom_linking_debug_log("Total courses processed: " . count($processed_courses));
        custom_linking_debug_log("Processed courses: " . print_r(array_keys($processed_courses), true));
        
        // Mark order as processed to prevent future duplicate processing
        update_post_meta($order_id, $order_meta_key, time());
        custom_linking_debug_log("Marked order {$order_id} as processed");

        custom_linking_debug_log("Completed processing order {$order_id}");

    } catch (Exception $e) {
        custom_linking_debug_log("Critical exception in order completion: " . $e->getMessage());
    }
}

/**
 * Get linked courses for a specific product ID
 * 
 * @param int $product_id The WooCommerce product ID
 * @return array Array of course link objects
 */
function custom_linking_get_courses_for_product($product_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    $results = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT course_id, type FROM $table_name WHERE product_id = %d",
            $product_id
        )
    );
    
    return $results ?: array();
}

/**
 * Grant course access to a user using MasterStudy LMS database patterns
 *
 * @param int $user_id The WordPress user ID
 * @param int $course_id The course ID (or bundle ID if link_type is 'bundle')
 * @param string $link_type The type of link (course or bundle)
 * @return bool True if access was granted successfully
 */
function custom_linking_grant_course_access($user_id, $course_id, $link_type = 'course') {
    global $wpdb;

    try {
        custom_linking_debug_log("Starting course access grant for user {$user_id}, course {$course_id}, type: {$link_type}");

        // Validate inputs
        if (!$user_id || !$course_id) {
            custom_linking_debug_log("Invalid user ID or course ID provided");
            return false;
        }

        // BUNDLE HANDLING: If this is a bundle, use the bundle access mechanism
        if ($link_type === 'bundle') {
            custom_linking_debug_log("BUNDLE DETECTED: Processing bundle {$course_id} for user {$user_id}");
            custom_linking_debug_log("Bundle processing: link_type = {$link_type}, course_id = {$course_id}");

            // Include the bundle completion file if not already loaded
            if (!function_exists('custom_linking_grant_bundle_access')) {
                custom_linking_debug_log("Loading bundle completion file...");
                require_once dirname(__FILE__) . '/bundle-completion.php';
            } else {
                custom_linking_debug_log("Bundle completion function already available");
            }

            // Use the existing bundle access function
            custom_linking_debug_log("Calling custom_linking_grant_bundle_access for user {$user_id}, bundle {$course_id}");
            $bundle_access_result = custom_linking_grant_bundle_access($user_id, $course_id);

            if ($bundle_access_result) {
                custom_linking_debug_log("SUCCESS: Bundle access granted for bundle {$course_id} to user {$user_id}");
                return true;
            } else {
                custom_linking_debug_log("FAILED: Bundle access failed for bundle {$course_id} to user {$user_id}");
                return false;
            }
        }

        // REGULAR COURSE HANDLING: Continue with normal course access logic
        custom_linking_debug_log("Processing regular course access for course {$course_id}");
        $access_granted = false;

        // Method 1: Try MasterStudy LMS user_courses table (most common pattern)
        $user_courses_table = $wpdb->prefix . 'stm_lms_user_courses';

        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$user_courses_table}'");

        if ($table_exists) {
            custom_linking_debug_log("Found MasterStudy LMS user_courses table: {$user_courses_table}");

            // Check if user already has access
            $existing_access = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM {$user_courses_table} WHERE user_id = %d AND course_id = %d",
                    $user_id,
                    $course_id
                )
            );

            if ($existing_access) {
                custom_linking_debug_log("User {$user_id} already has access to course {$course_id}");
                $access_granted = true;
            } else {
                // Insert new course access record
                
                /**
                 * Special handling for WordPress course (ID 49599)
                 * This ensures the WordPress course always gets a proper timestamp
                 * This fixes the issue where the WordPress course appears with 1970-01-01 date
                 * when purchased individually, causing duplicate enrollments
                 */
                if ($course_id == 49599) {
                    custom_linking_debug_log("Special handling for WordPress course ID 49599");
                    
                    // First, double-check across ALL enrollment records (regardless of timestamp)
                    $any_enrollment = $wpdb->get_var(
                        $wpdb->prepare(
                            "SELECT COUNT(*) FROM {$user_courses_table} WHERE user_id = %d AND course_id = %d",
                            $user_id,
                            $course_id
                        )
                    );
                    
                    if ($any_enrollment > 0) {
                        custom_linking_debug_log("WordPress course already enrolled (found {$any_enrollment} enrollment records). Updating timestamp.");
                        
                        // Update all existing records with current timestamp
                        $wpdb->query(
                            $wpdb->prepare(
                                "UPDATE {$user_courses_table} SET start_time = %d WHERE user_id = %d AND course_id = %d",
                                time(), // Use Unix timestamp instead of MySQL date
                                $user_id,
                                $course_id
                            )
                        );
                        
                        $result = true; // Consider this a successful operation
                    } else {
                        // No enrollment found, create a new one with EXPLICIT current timestamp
                        custom_linking_debug_log("Creating new WordPress course enrollment with explicit timestamp");
                        $result = $wpdb->insert(
                            $user_courses_table,
                            array(
                                'user_id' => $user_id,
                                'course_id' => $course_id,
                                'status' => 'enrolled',
                                'start_time' => time(), // Use Unix timestamp instead of MySQL date
                                'progress_percent' => 0,
                                'current_lesson_id' => 0
                            ),
                            array('%d', '%d', '%s', '%s', '%d', '%d')
                        );
                    }
                } else {
                    // Regular course processing for non-WordPress courses
                    // Using time() to get Unix timestamp instead of MySQL formatted time
                    // This ensures consistent timestamp storage across all courses
                    $current_timestamp = time(); // Get current Unix timestamp
                    
                    /**
                     * Store timestamp in the format expected by MasterStudy LMS
                     * This fixes the issue where courses show Jan 1, 1970 enrollment date
                     */
                    custom_linking_debug_log("Using Unix timestamp {$current_timestamp} for course {$course_id}");
                    
                    $result = $wpdb->insert(
                        $user_courses_table,
                        array(
                            'user_id' => $user_id,
                            'course_id' => $course_id,
                            'status' => 'enrolled',
                            'start_time' => $current_timestamp, // Use Unix timestamp instead of MySQL date
                            'progress_percent' => 0,
                            'current_lesson_id' => 0
                        ),
                        array('%d', '%d', '%s', '%d', '%d', '%d') // Changed %s to %d for timestamp
                    );
                }

                if ($result !== false) {
                    $access_granted = true;
                    custom_linking_debug_log("Method 1: Successfully enrolled user {$user_id} in course {$course_id} via user_courses table");
                } else {
                    custom_linking_debug_log("Method 1: Failed to insert into user_courses table: " . $wpdb->last_error);
                }
            }
        } else {
            custom_linking_debug_log("Method 1: MasterStudy LMS user_courses table not found");
        }

        // Method 2: Try user meta approach (fallback)
        if (!$access_granted) {
            try {
                // Add course to user's enrolled courses meta
                $user_courses = get_user_meta($user_id, 'stm_lms_courses', true);
                if (!is_array($user_courses)) {
                    $user_courses = array();
                }

                if (!in_array($course_id, $user_courses)) {
                    $user_courses[] = $course_id;
                    $meta_result = update_user_meta($user_id, 'stm_lms_courses', $user_courses);

                    if ($meta_result !== false) {
                        $access_granted = true;
                        custom_linking_debug_log("Method 2: Added course {$course_id} to user {$user_id} meta (stm_lms_courses)");
                    } else {
                        custom_linking_debug_log("Method 2: Failed to update user meta");
                    }
                } else {
                    custom_linking_debug_log("Method 2: User {$user_id} already has course {$course_id} in meta");
                    $access_granted = true;
                }
            } catch (Exception $e) {
                custom_linking_debug_log("Method 2: Exception in user meta: " . $e->getMessage());
            }
        }

        // Method 3: Try course students meta approach
        if (!$access_granted) {
            try {
                $course_students = get_post_meta($course_id, 'stm_lms_course_students', true);
                if (!is_array($course_students)) {
                    $course_students = array();
                }

                if (!in_array($user_id, $course_students)) {
                    $course_students[] = $user_id;
                    $meta_result = update_post_meta($course_id, 'stm_lms_course_students', $course_students);

                    if ($meta_result !== false) {
                        $access_granted = true;
                        custom_linking_debug_log("Method 3: Added user {$user_id} to course {$course_id} students meta");
                    } else {
                        custom_linking_debug_log("Method 3: Failed to update course students meta");
                    }
                } else {
                    custom_linking_debug_log("Method 3: User {$user_id} already in course {$course_id} students");
                    $access_granted = true;
                }
            } catch (Exception $e) {
                custom_linking_debug_log("Method 3: Exception in course meta: " . $e->getMessage());
            }
        }

        // Method 4: Direct database insertion into user_courses if table exists but previous method failed
        if (!$access_granted && $table_exists) {
            try {
                // Try a simpler insert
                $result = $wpdb->query(
                    $wpdb->prepare(
                        "INSERT IGNORE INTO {$user_courses_table} (user_id, course_id, status) VALUES (%d, %d, %s)",
                        $user_id,
                        $course_id,
                        'enrolled'
                    )
                );

                if ($result !== false) {
                    $access_granted = true;
                    custom_linking_debug_log("Method 4: Simple insert successful for user {$user_id}, course {$course_id}");
                } else {
                    custom_linking_debug_log("Method 4: Simple insert failed: " . $wpdb->last_error);
                }
            } catch (Exception $e) {
                custom_linking_debug_log("Method 4: Exception in direct insert: " . $e->getMessage());
            }
        }

        // Log the final result
        if ($access_granted) {
            custom_linking_debug_log("SUCCESS: Granted access to course {$course_id} for user {$user_id}");
        } else {
            custom_linking_debug_log("FAILED: Could not grant access to course {$course_id} for user {$user_id}");
        }

        return $access_granted;

    } catch (Exception $e) {
        custom_linking_debug_log("Critical exception in course access grant: " . $e->getMessage());
        return false;
    }
}

// Initialize the order completion hooks
custom_linking_init_order_hooks();
