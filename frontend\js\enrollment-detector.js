/**
 * Early Enrollment Detection for Custom Linking Plugin
 * 
 * This script runs early to detect if a user is already enrolled
 * and prevent any interference with Start Course functionality
 */

(function() {
    'use strict';
    
    // Run as soon as possible
    function detectEnrollmentEarly() {
        // Set global flag if not already set
        if (typeof window.customFreeCourseUserEnrolled === 'undefined') {
            window.customFreeCourseUserEnrolled = false;
        }
        
        // Quick DOM check for Start Course indicators
        var hasStartCourse = false;
        
        // Check common Start Course button selectors
        var startSelectors = [
            'a[href*="/lesson/"]',
            'a[href*="/curriculum/"]', 
            '.start-course',
            '.continue-course',
            '.stm-course-start-btn'
        ];
        
        for (var i = 0; i < startSelectors.length; i++) {
            if (document.querySelector(startSelectors[i])) {
                hasStartCourse = true;
                break;
            }
        }
        
        // Check by text content
        if (!hasStartCourse) {
            var allButtons = document.querySelectorAll('a, button');
            for (var j = 0; j < allButtons.length; j++) {
                var text = allButtons[j].textContent.toLowerCase().trim();
                if (text.includes('start course') || text.includes('continue course') || 
                    text.includes('resume course') || text.includes('take course')) {
                    hasStartCourse = true;
                    break;
                }
            }
        }
        
        if (hasStartCourse) {
            console.log('[Custom Linking] Early detection: User is enrolled - disabling course linking');
            window.customFreeCourseUserEnrolled = true;
            
            // Add class to body
            if (document.body) {
                document.body.classList.add('custom-linking-user-enrolled');
            } else {
                // Body not ready yet, wait for it
                document.addEventListener('DOMContentLoaded', function() {
                    document.body.classList.add('custom-linking-user-enrolled');
                });
            }
        }
    }
    
    // Run immediately if DOM is ready
    if (document.readyState !== 'loading') {
        detectEnrollmentEarly();
    } else {
        // Wait for DOM content to load
        document.addEventListener('DOMContentLoaded', detectEnrollmentEarly);
    }
    
    // Also run on window load as fallback
    window.addEventListener('load', detectEnrollmentEarly);
    
})();
