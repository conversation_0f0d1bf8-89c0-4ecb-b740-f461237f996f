# 🔗 Custom Linking Plugin - Release Notes
**Version:** 1.1.0  
**Author:** Ku<PERSON><PERSON> Mishra

## 📋 Table of Contents
1. [Overview](#overview)
2. [Core Features](#core-features)
3. [Required Plugins](#required-plugins)
4. [Installation](#installation)
5. [Usage Guide](#usage-guide)
6. [Technical Documentation](#technical-documentation)
7. [Plugin Architecture](#plugin-architecture)
8. [Advanced Features](#advanced-features)
9. [Edge Cases & Robust Handling](#edge-cases--robust-handling)
10. [File Structure & Components](#file-structure--components)
11. [Changelog](#changelog)
12. [Troubleshooting](#troubleshooting)

## 📝 Overview
The **Custom Linking Plugin** is an enterprise-level solution that creates a seamless integration between **MasterStudy LMS** courses and **WooCommerce** products. This robust plugin transforms your e-learning platform by enabling course purchases through WooCommerce's powerful checkout system while ensuring automatic course access after purchase.

**What this plugin does:**
- Links MasterStudy LMS courses and bundles to WooCommerce products
- Handles guest user redirection to login pages
- Manages free course enrollment (both linked and non-linked)
- Processes order completion and automatically grants course access
- Provides comprehensive duplicate prevention mechanisms
- Offers admin panel for managing links with search and pagination
- Includes debug controls for development and troubleshooting

**Purpose of the plugin:**
This plugin bridges the gap between LMS course management and e-commerce functionality, allowing educational platforms to leverage WooCommerce's powerful payment processing, checkout flows, and product management capabilities while maintaining the educational features of MasterStudy LMS.

## 🚀 Core Features

### 1. **Course-Product Linking System**
- Links individual MasterStudy LMS courses to WooCommerce products
- Supports course bundle linking through Course Bundler plugin integration
- Custom database table (`wp_linking_table`) for storing relationships
- Type differentiation between 'course' and 'bundle' links

### 2. **Guest User Management**
- **File Responsible:** `frontend/guestLoginHandler.php`, `frontend/js/guest-login-popup.js`
- Automatically redirects non-logged-in users to login page when clicking course buttons
- Configurable login URL (currently set to `/user-account/`)
- High-priority click interception to ensure guest redirect runs first
- Comprehensive button selector coverage including bundle buttons

### 3. **Free Course Handling**
- **Files Responsible:** `frontend/FreeCourse.php`, `frontend/js/FreeCourse.js`
- Separate handling for free courses with two scenarios:
  - **Linked Free Courses:** Free courses linked to WooCommerce products (follows normal purchase flow)
  - **Non-Linked Free Courses:** Direct enrollment without payment
- Automatic enrollment for logged-in users
- Login requirement for non-logged-in users accessing free courses
- Comprehensive access verification before enrollment

### 4. **Order Completion & Course Access**
- **Files Responsible:** `frontend/order-completion.php`, `frontend/bundle-completion.php`
- Automatic course access granting upon WooCommerce order completion
- Hooks into `woocommerce_order_status_completed` and `woocommerce_order_status_processing`
- Bundle processing with multiple fallback methods for course discovery
- Duplicate prevention to avoid multiple enrollments

### 5. **Admin Management Panel**
- **Files Responsible:** `admin/linkAdmin.php`, `admin/admin.php`, `admin/css/admin.css`, `admin/js/admin.js`
- Intuitive interface for creating and managing course-product links
- **Search Functionality:** Search by ID, course title, or product title
- **Pagination:** Handle large numbers of links efficiently (10 items per page)
- **Debug Settings:** Toggle console logging and debug file logging
- Form validation and error handling
- Bulk delete capabilities with confirmation

### 6. **Debug & Logging System**
- **Files Responsible:** `includes/debug.php`
- Configurable debug logging to plugin root `debug.log` file
- Console logging controls (can be silenced in production)
- Admin panel toggles for enabling/disabling debug features
- Comprehensive logging throughout all plugin operations

## 🔌 Required Plugins
This plugin requires the following plugins to be installed and active:

1. **MasterStudy LMS Learning Management System**
   - Path: `C:\xampp\htdocs\paylearn\wp-content\plugins\masterstudy-lms-learning-management-system`
   - **Purpose:** Provides the core LMS functionality, course management, and enrollment system

2. **WooCommerce**
   - Path: `C:\xampp\htdocs\paylearn\wp-content\plugins\woocommerce`
   - **Purpose:** Handles payment processing, checkout flow, and product management

3. **Course Bundler Plugin** (for bundle functionality)
   - Path: `C:\xampp\htdocs\code\wp-content\plugins\course-bundler`
   - **Purpose:** Enables course bundle creation and management (optional for basic course linking)

## 💻 Installation

1. **Upload the plugin**
   - Upload the `custom-linking-plugin` folder to the `/wp-content/plugins/` directory

2. **Activate the plugin**
   - Activate the plugin through the 'Plugins' menu in WordPress
   - Plugin automatically creates the `wp_linking_table` database table

3. **Verify dependencies**
   - Ensure WooCommerce and MasterStudy LMS are installed and active
   - For bundle functionality, ensure Course Bundler is installed and active

4. **Configure settings**
   - Navigate to **Custom Linking > Products & Courses** in WordPress admin
   - Configure debug settings as needed

## 📘 Usage Guide

### Step 1: Creating Course-Product Links
1. Navigate to **Custom Linking > Products & Courses**
2. Click **Add New Link**
3. Enter the following information:
   - **Course:** Select from dropdown (includes both courses and bundles)
   - **Product:** Select WooCommerce product from dropdown
   - **Type:** Select 'course' for individual courses or 'bundle' for course bundles
4. Click **Save Link**

### Step 2: Managing Existing Links
- **Search:** Use the search box to find links by ID, course name, or product name
- **Pagination:** Navigate through links using the pagination controls
- **Delete:** Remove links using the delete button (with confirmation)

### Step 3: Debug Configuration
1. In the admin panel, locate **Debug Settings**
2. Configure two toggles:
   - **Enable Console Debug:** Show/hide browser console output
   - **Enable Debug Log:** Write messages to `debug.log` in plugin root
3. Click **Save Debug Settings**

### Step 4: Frontend Behavior
Once links are created, the plugin automatically:
1. **For Logged-in Users:**
   - Overrides MasterStudy LMS "Get Course" button to redirect to WooCommerce
   - Adds linked products to cart and redirects to checkout
   - Processes order completion to grant course access

2. **For Guest Users:**
   - Redirects to login page (`/user-account/`) when clicking course buttons
   - Maintains return URL for post-login redirection

3. **For Free Courses:**
   - **Linked:** Follows normal WooCommerce flow (even if free)
   - **Non-linked:** Direct enrollment for logged-in users, login required for guests

## 🔧 Technical Documentation

### How the Plugin Works Behind the Scenes

#### 1. Database Structure
The plugin creates a custom database table `wp_linking_table` with the following structure:
```sql
CREATE TABLE wp_linking_table (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    course_id bigint(20) NOT NULL,
    product_id bigint(20) NOT NULL,
    type enum('course','bundle') NOT NULL DEFAULT 'course',
    PRIMARY KEY (id),
    KEY course_id (course_id),
    KEY product_id (product_id)
)
```

#### 2. Core Mechanisms

**Frontend Button Override System:**
- JavaScript intercepts MasterStudy LMS button clicks using comprehensive selectors
- Checks user login status and enrollment status before processing
- Makes AJAX calls to determine course-product relationships
- Redirects appropriately based on response (cart, product page, or course)

**Guest User Redirection:**
- High-priority JavaScript runs before other button handlers
- Captures clicks on course/bundle buttons for non-logged-in users
- Immediately redirects to configured login URL
- Uses both jQuery delegated events and native DOM event capturing

**WooCommerce Order Processing:**
- Hooks into `woocommerce_order_status_completed` and `woocommerce_order_status_processing`
- Analyzes order items to find linked courses/bundles
- Implements sophisticated duplicate prevention mechanisms
- Grants access using multiple MasterStudy LMS enrollment methods

**Free Course Processing:**
1. Detects if course is free using price and meta data analysis
2. Checks if free course is linked to a product
3. For linked courses: follows normal purchase flow
4. For non-linked courses: direct enrollment with access verification
5. Comprehensive enrollment using multiple MasterStudy LMS methods

**Bundle Processing:**
1. Uses Course Bundler's native enrollment system when available
2. Falls back to multiple methods for finding courses in bundles:
   - Course Bundler's bundle courses function
   - MasterStudy LMS bundle metadata
   - Database table queries
   - WordPress post content parsing
3. Implements comprehensive duplicate prevention
4. Processes each course individually with verification

#### 3. Duplicate Prevention System
**Multiple Verification Layers:**
- Initial access check before any processing
- Pre-insertion database verification
- Post-insertion verification for safety
- Uses 8 different access checking strategies:
  1. MasterStudy LMS native access functions
  2. User meta `stm_lms_courses` array
  3. User meta `stm_lms_course_passed` array
  4. User meta `stm_lms_user_courses` array
  5. Course-specific enrollment meta
  6. User capabilities system
  7. Direct database table queries
  8. Course students meta data

#### 4. AJAX Architecture
**Endpoints:**
- `custom_linking_course_request`: Handle course purchase requests
- `custom_linking_process_bundle`: Handle bundle purchase requests  
- `custom_linking_process_free_course`: Handle free course access requests

**Security:**
- WordPress nonce verification for all AJAX requests
- Capability checks for admin functions
- Input sanitization and validation
- CSRF protection

### 6. **Comprehensive Deactivation System**
The plugin implements a complete deactivation system that ensures no traces remain after plugin removal.

**Architecture:**
- **Interactive confirmation**: Custom modal prevents accidental deactivation
- **Optional data export**: CSV export before deletion with comprehensive data
- **Complete cleanup**: Removes database tables, WordPress options, and files
- **Graceful error handling**: Fallback cleanup methods ensure removal even if errors occur

**Deactivation Process Flow:**
1. **User clicks deactivate** → JavaScript intercepts the click
2. **Data count check** → AJAX request to get number of records
3. **Confirmation modal** → Shows warning and export options
4. **Optional export** → If selected, exports data to CSV and triggers download
5. **Complete cleanup** → Removes all database tables, options, and files
6. **Plugin deactivation** → WordPress completes the deactivation process

**What Gets Removed:**
- **Database tables:** `wp_linking_table` completely dropped
- **WordPress options:** All plugin-specific options removed:
  - `custom_linking_enable_console`
  - `custom_linking_enable_debug_log`
  - `custom_linking_plugin_version`
- **Files:** Debug logs and temporary files deleted
- **Cache:** WordPress cache cleared for complete removal

**CSV Export Format:**
```csv
"Link ID","Course ID","Product ID","Type","Course Title","Product Title","Course Status","Product Status","Export Date"
"1","12345","67890","course","Course Title","Product Title","publish","publish","2025-07-04 12:00:00"
```

**Security Measures:**
- AJAX nonce verification for all requests
- User capability checks (`activate_plugins`)
- Protected exports directory with .htaccess rules
- Automatic cleanup of export files after 1 hour

**Files Involved:**
- `database/Deactivator.php`: Core cleanup logic and CSV export functionality
- `admin/deactivationHandler.php`: AJAX handlers and admin integration
- `admin/js/deactivation.js`: Modal interface and user interaction
- `admin/css/deactivation.css`: Professional modal styling
- `exports/`: Temporary directory for CSV files

## 🏗️ Advanced Features

### 1. **Console Output Control**
- Production-ready console silencing capability
- Admin toggle to enable/disable console output
- Automatic override of console methods when disabled
- Separate control for debug file logging

### 2. **Dynamic Button Detection**
- MutationObserver for dynamically added buttons
- Comprehensive button selector coverage
- High-priority event capturing to prevent conflicts
- Fallback mechanisms for different LMS themes

### 3. **Comprehensive Error Handling**
- Graceful fallbacks for missing dependencies
- Detailed error logging with context
- User-friendly error messages
- Automatic recovery mechanisms

### 4. **Multi-Environment Support**
- Development vs production configurations
- Configurable login URLs
- Environment-specific debug levels
- Cross-browser compatibility

### 5. **Product Type Filtering System**
The plugin implements a comprehensive filtering system to restrict course-product linking to specific WooCommerce product types (by default, only simple products).

**Architecture:**
- **Server-side filtering**: `custom_linking_get_available_products()` function in `admin/admin.php` filters products by type
- **Client-side validation**: JavaScript validation in `admin/js/admin.js` provides real-time feedback
- **Multi-layer validation**: Both frontend and backend validation to ensure data integrity

**How to Modify Allowed Product Types:**

1. **Server-side Filter (admin/admin.php):**
```php
// In custom_linking_get_available_products() function
// Current filter (line ~45):
$allowed_types = array('simple');

// To allow additional types, modify to:
$allowed_types = array('simple', 'variable', 'grouped');
// Or for all types: $allowed_types = array(); // Empty array = no filtering
```

2. **Client-side Validation (admin/js/admin.js):**
```javascript
// In validateProductType() function (line ~25):
// Current validation:
if (productType !== 'simple') {
    // Show error
}

// To allow additional types, modify to:
var allowedTypes = ['simple', 'variable', 'grouped'];
if (allowedTypes.indexOf(productType) === -1) {
    // Show error
}
```

3. **Update User Interface Text:**
- Modify the dropdown note in `admin/admin.php` (line ~140) to reflect new allowed types
- Update error messages in `admin/linkAdmin.php` if needed

**Files to Modify for Custom Product Types:**
- `admin/admin.php`: Update `$allowed_types` array and dropdown text
- `admin/js/admin.js`: Update validation logic and allowed types array
- `admin/linkAdmin.php`: Update error messages (optional)

## ⚡ Edge Cases & Robust Handling

### 1. **Duplicate Course Access Prevention**
**Problem:** User receiving duplicate course access when course is available through both individual purchase and bundle
**Solution:** 
- Track processed courses per order
- Prioritize bundle access over individual course access
- Multiple verification layers before granting access
- Comprehensive access checking using 8 different methods

### 2. **Bundle Child Product Filtering**
**Problem:** Bundle child products creating duplicate enrollments
**Solution:**
- Detect bundle child products via meta data (`_asnp_wepb_parent_id`)
- Skip processing for bundle child products
- Process only parent bundle products

### 3. **Free Course Edge Cases**
**Problem:** Free courses behaving differently based on linking status
**Solution:**
- Separate handling paths for linked vs non-linked free courses
- Linked free courses follow normal WooCommerce flow
- Non-linked free courses get direct enrollment
- Access verification before and after enrollment

### 4. **Guest User Button Conflicts**
**Problem:** Multiple button handlers competing for click events
**Solution:**
- High-priority event capturing phase
- Comprehensive button selector coverage
- Early exit when guest redirect is active
- Prevention of event bubbling conflicts

### 5. **Course Bundler Integration Edge Cases**
**Problem:** Multiple ways course bundles store course relationships
**Solution:**
- Native Course Bundler enrollment when available
- Multiple fallback methods for course discovery
- Database table queries as secondary option
- Post meta parsing as tertiary option
- Content parsing as final fallback

### 6. **WordPress Course Timestamp Issues**
**Problem:** Specific course (ID 49599) showing incorrect enrollment dates
**Solution:**
- Special handling for problematic course IDs
- Proper timestamp generation for enrollment records
- Verification of enrollment success after processing

### 7. **AJAX Response Handling**
**Problem:** Various response formats and error conditions
**Solution:**
- Comprehensive response validation
- Multiple redirect scenarios (cart, product page, course)
- Error recovery with URL extraction from error responses
- Timeout and network error handling

### 8. **User Enrollment State Detection**
**Problem:** Determining if user is already enrolled in a course
**Solution:**
- Multiple enrollment detection strategies
- Cross-verification using different data sources
- Fallback mechanisms for different LMS configurations
- Cache clearing for immediate access detection

## 📂 File Structure & Components

### **Core Files**
- **`custom-linking-plugin.php`**: Main plugin file, handles initialization, activation/deactivation hooks, and WordPress integration
- **`class-core.php`**: Core class that manages plugin components, initialization, and component loading

### **Admin Interface** (`admin/`)
- **`linkAdmin.php`**: Admin menu registration, page setup, and script/style loading
- **`admin.php`**: Main admin page functionality, form handling, pagination, search, and debug settings
- **`css/admin.css`**: Admin panel styling including form styles, table layouts, pagination controls, and responsive design
- **`js/admin.js`**: Admin JavaScript for form validation, AJAX interactions, notice dismissal, and delete confirmations

### **Frontend Functionality** (`frontend/`)

#### **Core Frontend Files**
- **`linkFrontend.php`**: Main frontend initialization, script loading, AJAX handlers, and button override coordination
- **`courselinking.php`**: AJAX request handler for course-product link queries and user login verification

#### **Guest User Management**
- **`guestLoginHandler.php`**: Server-side guest redirect configuration and script enqueuing
- **`js/guest-login-popup.js`**: Client-side guest user redirection logic with comprehensive button detection

#### **Free Course System**
- **`FreeCourse.php`**: Free course detection, enrollment logic, access verification, and comprehensive enrollment methods
- **`js/FreeCourse.js`**: Frontend free course processing, AJAX handling, and user interface feedback

#### **Bundle Processing**
- **`bundleLinkFrontend.php`**: Bundle frontend initialization and script loading
- **`bundleLinking.php`**: AJAX handler for bundle purchase processing and WooCommerce integration
- **`bundle-completion.php`**: Order completion handler for bundle purchases with sophisticated duplicate prevention
- **`js/custom-bundle-linking.js`**: Client-side bundle button interception and purchase flow handling

#### **Order Processing**
- **`order-completion.php`**: WooCommerce order completion processing, course access granting, and comprehensive enrollment handling

#### **Main JavaScript**
- **`js/custom-linking.js`**: Primary frontend JavaScript with button interception, AJAX processing, course ID detection, and user enrollment state management

### **Utility & Support** (`includes/`)
- **`debug.php`**: Debug logging system with configurable toggles, file logging, and console output controls
- **`integration.php`**: Shared integration functions, utility helpers, and cross-module communication
- **`linkIncludes.php`**: Common utility functions and shared functionality

### **Database Management** (`database/`)
- **`Activator.php`**: Plugin activation handler and database table creation with proper schema
- **`Deactivator.php`**: Plugin deactivation handler and cleanup operations  
- **`linkDatabase.php`**: Database interaction functions, query helpers, and data management operations

### **Configuration Files**
- **`ReleaseNotes.md`**: Comprehensive documentation (this file)
- **`debug.log`**: Debug output file (created automatically when debug logging is enabled)

## 🎯 Component Responsibilities

### **Database Layer**
- **Table Schema:** `wp_linking_table` with course_id, product_id, and type fields
- **Data Operations:** Create, read, update, delete operations for course-product links
- **Activation/Deactivation:** Automatic table creation and cleanup

### **Admin Layer**
- **Link Management:** Create, edit, delete course-product relationships
- **Search & Pagination:** Handle large datasets efficiently  
- **Debug Controls:** Configure logging and console output
- **Validation:** Ensure data integrity and user input validation

### **Frontend Processing Layer**
- **Button Interception:** Override default LMS behavior for linked courses
- **User State Management:** Handle logged-in vs guest user scenarios
- **AJAX Communication:** Secure server-client data exchange
- **Purchase Flow:** Guide users through appropriate purchase or enrollment paths

### **Integration Layer**
- **WooCommerce Integration:** Cart management, order processing, payment completion
- **MasterStudy LMS Integration:** Course access granting, enrollment management, user verification
- **Course Bundler Integration:** Bundle course discovery and enrollment processing

### **Security & Error Handling**
- **Nonce Verification:** CSRF protection for all AJAX requests
- **Input Sanitization:** Clean and validate all user inputs
- **Error Recovery:** Graceful handling of edge cases and failures
- **Access Control:** Capability checks and permission verification

## 📊 Plugin Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    WordPress Core                           │
├─────────────────────────────────────────────────────────────┤
│                 Custom Linking Plugin                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Admin     │  │  Frontend   │  │     Database        │  │
│  │  Interface  │  │ Processing  │  │    Management       │  │
│  │             │  │             │  │                     │  │
│  │ • Link Mgmt │  │ • Button    │  │ • Table Creation    │  │
│  │ • Search    │  │   Override  │  │ • CRUD Operations   │  │
│  │ • Pagination│  │ • AJAX      │  │ • Data Validation   │  │
│  │ • Debug     │  │   Handlers  │  │                     │  │
│  └─────────────┘  │ • User Mgmt │  └─────────────────────┘  │
│                   │ • Free      │                          │
│                   │   Courses   │                          │
│                   │ • Bundles   │                          │
│                   └─────────────┘                          │
├─────────────────────────────────────────────────────────────┤
│              External Plugin Integration                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │WooCommerce  │  │MasterStudy  │  │  Course Bundler     │  │
│  │             │  │    LMS      │  │    (Optional)       │  │
│  │ • Cart Mgmt │  │ • Course    │  │ • Bundle Discovery  │  │
│  │ • Orders    │  │   Access    │  │ • Course Relations  │  │
│  │ • Products  │  │ • Users     │  │ • Native Enrollment │  │
│  │ • Payments  │  │ • Enrollment│  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 Data Flow Architecture

### **User Interaction Flow**
1. **User clicks course button** → Frontend JavaScript intercepts
2. **Guest check** → Redirect to login if not logged in  
3. **Enrollment check** → Skip processing if already enrolled
4. **AJAX request** → Query course-product relationships
5. **Response processing** → Redirect to cart, product, or course
6. **Purchase completion** → WooCommerce order processing
7. **Access granting** → Automatic course enrollment

### **Admin Management Flow**
1. **Admin creates link** → Form validation and submission
2. **Database storage** → Secure data insertion with validation
3. **Search/filter** → Dynamic query building with pagination
4. **Debug configuration** → Settings storage and script localization
5. **Link deletion** → Confirmation and secure removal

### **Bundle Processing Flow**
1. **Bundle purchase** → WooCommerce order completion
2. **Bundle detection** → Identify bundle products in order
3. **Course discovery** → Multiple methods to find bundle courses
4. **Duplicate prevention** → Comprehensive access verification
5. **Enrollment processing** → Individual course access granting
6. **Verification** → Post-enrollment access confirmation

## 📝 Changelog

### Version 1.2.0 – Comprehensive Plugin Deactivation System (2025-07-04)
- **Complete Data Cleanup:** Implemented comprehensive deactivation system that removes all traces of plugin presence
- **Interactive Deactivation Confirmation:** 
  - Custom modal dialog appears when user attempts to deactivate the plugin
  - Shows count of records that will be permanently deleted
  - Provides clear warning about permanent data loss
- **Optional Data Export:** 
  - User can choose to export all linking data to CSV before deactivation
  - Export includes course titles, product titles, link types, and status information
  - Automatic download of export file when selected
  - Export files are automatically cleaned up after 1 hour
- **Multi-layer Cleanup Process:**
  - **Database cleanup:** Removes custom `wp_linking_table` completely
  - **Options cleanup:** Removes all WordPress options (`custom_linking_enable_console`, `custom_linking_enable_debug_log`, `custom_linking_plugin_version`)
  - **File cleanup:** Removes debug.log and any temporary files
  - **Cache cleanup:** Clears WordPress cache to ensure complete removal
- **Enhanced User Experience:**
  - Professional modal interface with progress indicators
  - Real-time feedback during export and deactivation process
  - Graceful error handling with fallback options
  - Responsive design for mobile devices
- **Security Features:**
  - AJAX nonce verification for all export requests
  - Capability checks to ensure only authorized users can export data
  - Protected exports directory with .htaccess rules
- **Files Added:**
  - `admin/deactivationHandler.php`: Server-side deactivation logic and AJAX handlers
  - `admin/js/deactivation.js`: Client-side modal and confirmation interface
  - `admin/css/deactivation.css`: Professional styling for deactivation modal
  - `exports/`: Directory for temporary CSV export files
- **Database Enhancement:** Enhanced `Deactivator.php` with complete cleanup methods and CSV export functionality
- **Future-Proof Design:** Clean reinstallation capability - activating plugin after deactivation recreates all necessary components

### Version 1.1.0 – Admin Debug Controls & Console Silencing (2025-06-14)
- **Debug Settings Enhancement:** Added comprehensive debug controls in admin panel with two toggles:
  1. **Enable Console Debug** – Show/hide all plugin `console.*` output for production environments
  2. **Enable Debug Log** – Write detailed messages to `debug.log` in plugin root directory
- **Options API Integration:** Each toggle stored via WordPress Options API (`custom_linking_enable_console`, `custom_linking_enable_debug_log`)
- **Dynamic Debug Constants:** Core constants in `includes/debug.php` now read options dynamically instead of hardcoded values
- **Frontend Console Control:** All frontend scripts (`custom-linking.js`, `custom-bundle-linking.js`, `guest-login-popup.js`) detect toggle via localized data and:
  • Emit full logging when enabled for development
  • Override `console.log/error/warn/info/debug` with no-ops when disabled to guarantee production silence
- **PHP Integration:** All PHP enqueue functions (`linkFrontend.php`, `bundleLinkFrontend.php`, `guestLoginHandler.php`) now localize `console_enabled` flag for JavaScript
- **User Experience:** Success notice added after saving debug settings with immediate feedback
- **Backward Compatibility:** Minor cleanup and fallback logic for legacy `debug` field to maintain compatibility

### Version 1.1.0 – Admin Panel Search Functionality (2025-06-14)
- **Advanced Search:** Added comprehensive search functionality in admin panel to locate products and courses efficiently
- **Multi-field Search:** Search supports both numeric IDs and text-based title matching
- **Intelligent Query:** Numeric searches match exact IDs (link ID, course ID, product ID), text searches use LIKE matching on titles
- **Real-time Filtering:** Search results update immediately with proper URL parameter handling
- **Search Persistence:** Search terms persist through pagination and navigation

### Version 1.1.0 – Admin Panel Pagination System (2025-06-14)
- **Scalable Pagination:** Added robust pagination system to handle large numbers of links efficiently
- **Configurable Display:** 10 items per page with full pagination controls (first, previous, next, last)
- **Smart Navigation:** Pagination includes current page indicators and total page counts
- **Search Integration:** Pagination works seamlessly with search functionality
- **Performance Optimization:** Efficient database queries with LIMIT/OFFSET for improved performance on large datasets

### Version 1.1.0 – Product Type Filtering System (2025-07-04)
- **Simple Product Restriction:** Implemented comprehensive filtering to restrict course-product linking to only simple WooCommerce products
- **Admin Panel Enhancement:** 
  - Product dropdown now shows only simple products by default
  - Added informational note and placeholder text indicating simple products only restriction
  - Enhanced product selection validation with real-time feedback
- **Multi-layer Validation:**
  - **Client-side validation:** JavaScript validates product type before form submission
  - **Real-time validation:** Immediate feedback when selecting non-simple products
  - **Server-side validation:** PHP double-checks product type before saving to database
- **User Experience Improvements:**
  - Clear error messages explaining product type restrictions
  - Visual error styling for invalid product selections
  - Comprehensive help text guiding users to select appropriate products
- **Customizable Filter Architecture:** Designed for easy modification to allow additional product types if needed
- **Files Modified:**
  - `admin/admin.php`: Enhanced `custom_linking_get_available_products()` function with filtering logic
  - `admin/js/admin.js`: Added client-side validation and real-time feedback
  - `admin/css/admin.css`: Added error styling for validation states
  - `admin/linkAdmin.php`: Added product type data localization for JavaScript validation

### Version 1.0.0 - Initial Release
- **Core Functionality:** Complete course-product linking system with WooCommerce integration
- **Admin Interface:** Intuitive admin panel for managing course-product relationships
- **Course & Bundle Support:** Full support for individual courses and course bundles via Course Bundler plugin
- **Debug System:** Comprehensive logging system with configurable output levels
- **WooCommerce Integration:** Complete order processing with automatic course access granting
- **Guest Management:** Automatic redirection system for non-logged-in users
- **Free Course Handling:** Specialized processing for free courses (both linked and non-linked)
- **Duplicate Prevention:** Advanced duplicate enrollment prevention for WordPress course (ID 49599) and all other courses
- **Bundle Processing:** Sophisticated bundle course discovery with multiple fallback methods
- **AJAX Architecture:** Secure AJAX system for frontend-backend communication
- **Edge Case Handling:** Comprehensive handling of various edge cases and error conditions
- **Multi-environment Support:** Development and production configuration options

## ❓ Troubleshooting

### Common Issues & Solutions:

#### 1. **Course access not granted after purchase**
**Symptoms:** User completes purchase but doesn't receive course access
**Solutions:**
- Check if the product is correctly linked to the course in admin panel
- Verify that the order status changed to "Processing" or "Completed"
- Check the debug log (`debug.log`) for enrollment errors
- Verify MasterStudy LMS tables exist and are accessible
- Ensure user has proper WordPress capabilities

#### 2. **Bundle courses not showing up**
**Symptoms:** Bundle purchase doesn't grant access to individual courses
**Solutions:**
- Ensure the Course Bundler plugin is active and functioning
- Verify the bundle contains courses and they're properly configured
- Check if the bundle is correctly linked as type 'bundle' in admin panel
- Review debug logs for bundle discovery issues
- Verify Course Bundler database tables exist

#### 3. **Guest users not redirecting to login**
**Symptoms:** Non-logged-in users can click course buttons without redirection
**Solutions:**
- Verify guest redirect JavaScript is loading (`guest-login-popup.js`)
- Check console for JavaScript errors that might prevent execution
- Ensure login URL is correctly configured in `guestLoginHandler.php`
- Verify button selectors match your theme's course buttons
- Check if other plugins are conflicting with JavaScript execution

#### 4. **Free courses not working properly**
**Symptoms:** Free courses don't enroll users or behave inconsistently
**Solutions:**
- Verify course is properly marked as free (price = 0)
- Check if free course is linked to a product (determines handling path)
- Review free course processing logs for enrollment failures
- Ensure user has sufficient WordPress capabilities for enrollment
- Verify MasterStudy LMS enrollment functions are available

#### 5. **Duplicate course access (Enhanced Prevention)**
**Symptoms:** Users receive multiple enrollments for the same course
**Solutions:**
- The plugin now includes comprehensive duplicate prevention
- Check debug logs for "DUPLICATE PREVENTION" messages
- Verify WordPress course (ID 49599) specific handling is working
- Ensure bundle priority system is preventing conflicts
- Review comprehensive access checking functions

#### 6. **Admin panel search not working**
**Symptoms:** Search functionality doesn't return expected results
**Solutions:**
- Verify database table integrity (`wp_linking_table`)
- Check for proper JOIN relationships between tables
- Ensure course and product posts exist and are published
- Clear any database caches that might affect results
- Review search query construction in debug logs

#### 7. **Pagination issues**
**Symptoms:** Pagination controls not working or displaying incorrectly
**Solutions:**
- Check URL parameter handling for `paged` parameter
- Verify total count calculation matches actual results
- Ensure pagination CSS is loading properly
- Check for conflicts with other plugins affecting pagination
- Review database query LIMIT/OFFSET calculations

#### 8. **Debug logging not working**
**Symptoms:** Debug logs not being written or console output missing
**Solutions:**
- Check debug toggle settings in admin panel
- Verify file permissions on plugin directory for log writing
- Ensure `debug.log` file is writable or can be created
- Check console for JavaScript errors preventing debug output
- Verify debug constants are properly defined and accessible

### Debug Information Access:
- **Debug logs:** Located in `/wp-content/plugins/custom-linking-plugin/debug.log`
- **Console output:** Enable via admin panel debug settings
- **WordPress debug:** Set `WP_DEBUG` to `true` in `wp-config.php` for enhanced logging
- **Database inspection:** Use phpMyAdmin or similar to examine `wp_linking_table`

### Performance Considerations:
- **Large datasets:** Pagination helps manage display of many links
- **Search optimization:** Database indexes on course_id and product_id improve query performance
- **JavaScript efficiency:** Delegated event handling reduces memory usage
- **AJAX timeouts:** 15-second timeout prevents hanging requests
- **Cache management:** Plugin clears relevant caches after enrollment operations

## 💡 Support and Contact

For support, questions, or feature requests, please contact:
- **Email:** <EMAIL>
- **Website:** https://vedmg.com

### Before Contacting Support:
1. Check this documentation for solutions
2. Review debug logs for error messages
3. Verify all required plugins are active and updated
4. Test with default WordPress theme to rule out theme conflicts
5. Disable other plugins temporarily to identify conflicts

### When Reporting Issues:
- Include WordPress version and plugin versions
- Provide relevant debug log excerpts
- Describe steps to reproduce the issue
- Include screenshots if applicable
- Mention any recent changes to your setup
