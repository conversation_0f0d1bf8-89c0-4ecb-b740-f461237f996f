<?php
/**
 * Shortcode functionality for Custom Linking Plugin
 * 
 * This file handles the [linking] shortcode
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize shortcode functionality
 */
function custom_linking_init_shortcode() {
    // Register the shortcodes
    add_shortcode('linking', 'custom_linking_shortcode_handler');
    add_shortcode('linking-home', 'custom_linking_home_shortcode_handler');
    add_shortcode('linking-enrolled', 'custom_linking_enrolled_shortcode_handler');
    add_shortcode('linking-cart', 'custom_linking_cart_shortcode_handler');
    
    // Enqueue scripts and styles
    add_action('wp_enqueue_scripts', 'custom_linking_enqueue_shortcode_assets');
}

/**
 * Handle the [linking] shortcode
 */
function custom_linking_shortcode_handler($atts) {
    // Parse shortcode attributes
    $atts = shortcode_atts(array(
        'type' => 'all', // all, course, bundle
        'limit' => '10',
        'id' => '',
    ), $atts);
    
    // Get dynamic URLs
    $home_url = home_url('/');
    $enrolled_courses_url = home_url('/user-account/enrolled-courses/');
    $cart_url = home_url('/cart/');
    
    // Start output buffering
    ob_start();
    ?>
    <div class="vedmg-linking-container" data-type="<?php echo esc_attr($atts['type']); ?>" data-limit="<?php echo esc_attr($atts['limit']); ?>">
        <div class="vedmg-linking-item">
            <div class="vedmg-navigation-buttons">
                <a href="<?php echo esc_url($home_url); ?>" class="vedmg-nav-btn vedmg-home-btn">
                    Go to Home Page
                </a>
                <a href="<?php echo esc_url($enrolled_courses_url); ?>" class="vedmg-nav-btn vedmg-courses-btn">
                    Go to Enrolled Courses
                </a>
                <?php if (is_user_logged_in()): ?>
                    <a href="<?php echo esc_url($cart_url); ?>" class="vedmg-nav-btn vedmg-cart-btn">
                        Go to Cart
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php
    
    return ob_get_clean();
}

/**
 * Handle the [linking-home] shortcode
 */
function custom_linking_home_shortcode_handler($atts) {
    // Parse shortcode attributes
    $atts = shortcode_atts(array(
        'align' => 'center', // center, left, right
    ), $atts);
    
    // Get dynamic URLs
    $home_url = home_url('/');
    
    // Determine alignment class
    $align_class = '';
    if ($atts['align'] === 'left') {
        $align_class = ' align-left';
    } elseif ($atts['align'] === 'right') {
        $align_class = ' align-right';
    }
    
    // Start output buffering
    ob_start();
    ?>
    <div class="vedmg-linking-container single-button<?php echo $align_class; ?>">
        <div class="vedmg-linking-item single-button">
            <div class="vedmg-navigation-buttons single-button<?php echo $align_class; ?>">
                <a href="<?php echo esc_url($home_url); ?>" class="vedmg-nav-btn vedmg-home-btn">
                    Go to Home Page
                </a>
            </div>
        </div>
    </div>
    <?php
    
    return ob_get_clean();
}

/**
 * Handle the [linking-enrolled] shortcode
 */
function custom_linking_enrolled_shortcode_handler($atts) {
    // Parse shortcode attributes
    $atts = shortcode_atts(array(
        'align' => 'center', // center, left, right
    ), $atts);
    
    // Get dynamic URLs
    $enrolled_courses_url = home_url('/user-account/enrolled-courses/');
    
    // Determine alignment class
    $align_class = '';
    if ($atts['align'] === 'left') {
        $align_class = ' align-left';
    } elseif ($atts['align'] === 'right') {
        $align_class = ' align-right';
    }
    
    // Start output buffering
    ob_start();
    ?>
    <div class="vedmg-linking-container single-button<?php echo $align_class; ?>">
        <div class="vedmg-linking-item single-button">
            <div class="vedmg-navigation-buttons single-button<?php echo $align_class; ?>">
                <a href="<?php echo esc_url($enrolled_courses_url); ?>" class="vedmg-nav-btn vedmg-courses-btn">
                    Go to Enrolled Courses
                </a>
            </div>
        </div>
    </div>
    <?php
    
    return ob_get_clean();
}

/**
 * Handle the [linking-cart] shortcode
 */
function custom_linking_cart_shortcode_handler($atts) {
    // Check if user is logged in - if not, return nothing
    if (!is_user_logged_in()) {
        return ''; // Return absolutely nothing for non-logged-in users
    }
    
    // Parse shortcode attributes
    $atts = shortcode_atts(array(
        'align' => 'center', // center, left, right
    ), $atts);
    
    // Get dynamic URLs
    $cart_url = home_url('/cart/');
    
    // Determine alignment class
    $align_class = '';
    if ($atts['align'] === 'left') {
        $align_class = ' align-left';
    } elseif ($atts['align'] === 'right') {
        $align_class = ' align-right';
    }
    
    // Start output buffering
    ob_start();
    ?>
    <div class="vedmg-linking-container single-button<?php echo $align_class; ?>">
        <div class="vedmg-linking-item single-button">
            <div class="vedmg-navigation-buttons single-button<?php echo $align_class; ?>">
                <a href="<?php echo esc_url($cart_url); ?>" class="vedmg-nav-btn vedmg-cart-btn">
                    Go to Cart
                </a>
            </div>
        </div>
    </div>
    <?php
    
    return ob_get_clean();
}

/**
 * Enqueue shortcode assets
 */
function custom_linking_enqueue_shortcode_assets() {
    // Only enqueue if shortcode is being used
    global $post;
    if (is_a($post, 'WP_Post') && (has_shortcode($post->post_content, 'linking') || 
                                   has_shortcode($post->post_content, 'linking-home') || 
                                   has_shortcode($post->post_content, 'linking-enrolled') ||
                                   has_shortcode($post->post_content, 'linking-cart'))) {
        
        // Enqueue CSS
        wp_enqueue_style(
            'custom-linking-shortcode',
            CUSTOM_LINKING_PLUGIN_URL . 'shortcode/css/shortcode.css',
            array(),
            CUSTOM_LINKING_PLUGIN_VERSION
        );
        
        // Enqueue JS
        wp_enqueue_script(
            'custom-linking-shortcode',
            CUSTOM_LINKING_PLUGIN_URL . 'shortcode/js/shortcode.js',
            array(),
            CUSTOM_LINKING_PLUGIN_VERSION,
            true
        );
        
        // Localize script data
        wp_localize_script(
            'custom-linking-shortcode',
            'vedmgLinkingData',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('custom_linking_nonce'),
                'logged_in' => is_user_logged_in() ? '1' : '0',
            )
        );
    }
}

// Initialize shortcode
add_action('init', 'custom_linking_init_shortcode');