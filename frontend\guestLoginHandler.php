<?php
/**
 * Guest Redirect Handler – Custom Linking Plugin
 * ----------------------------------------------
 * Goal: when a visitor (guest) clicks MasterStudy LMS “Get Course” buttons
 *        they should be redirected to the dedicated login / account page.
 * No pop-ups, no AJAX, no course-id logic.
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Enqueue tiny JS that overrides button click for guests.
 */
function clp_enqueue_guest_redirect_script() {
    if ( is_user_logged_in() || is_admin() ) {
        return; // members unaffected
    }
    
    $plugin_url = plugin_dir_url( dirname( __FILE__ ) );
    $ver        = defined( 'CUSTOM_LINKING_PLUGIN_VERSION' ) ? CUSTOM_LINKING_PLUGIN_VERSION : '1.0.0';
    
    // Load with higher priority than other scripts to ensure guest redirect runs first
    wp_enqueue_script( 'clp-guest-redirect', $plugin_url . 'frontend/js/guest-login-popup.js', array( 'jquery' ), $ver, false );
    
    // IMPORTANT: Always use the full login URL for guests. Do not change unless you know what you are doing.
    //$login_url = 'http://localhost/paylearn/user-account/'; // Fallback: full login URL for guests
    
    
    wp_localize_script( 'clp-guest-redirect', 'clpGuest', array(
        'logged_in' => '0',
        
        'login_url' => home_url( '/user-account/' ),
        'debug'     => defined('CUSTOM_LINKING_DEBUG') && CUSTOM_LINKING_DEBUG,
        'console_enabled' => (bool) get_option( 'custom_linking_enable_console', false ),
    ) );
}
add_action( 'wp_enqueue_scripts', 'clp_enqueue_guest_redirect_script', 5 ); // Higher priority to load before other scripts

//! this was previouslt used in palce of login url this was dynamic i made that static we will change the url as needed to redirect anywhere to the non logged user
// 'login_url' => home_url( "/user-account/" ), // Changed to MasterStudy user login page
