// Vanilla JS for [linking] shortcode navigation
document.addEventListener('DOMContentLoaded', function() {
    console.log('Linking shortcode JS loaded');
    
    // Find all linking containers
    var linkingContainers = document.querySelectorAll('.vedmg-linking-container');
    
    linkingContainers.forEach(function(container) {
        console.log('Found linking container:', container);
        
        // Add click tracking for navigation buttons
        var navButtons = container.querySelectorAll('.vedmg-nav-btn');
        
        navButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                var buttonType = this.classList.contains('vedmg-home-btn') ? 'home' : 'courses';
                var destination = this.href;
                
                console.log('Navigation button clicked:', buttonType, 'to:', destination);
                
                // Add visual feedback
                this.style.transform = 'translateY(2px)';
                
                // Reset transform after a short delay
                setTimeout(function() {
                    button.style.transform = '';
                }, 150);
                
                // Allow normal navigation (don't prevent default)
            });
        });
    });
    
    // Add smooth scroll effect if needed (optional enhancement)
    var smoothScrollButtons = document.querySelectorAll('.vedmg-nav-btn[href^="#"]');
    smoothScrollButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            var target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
});