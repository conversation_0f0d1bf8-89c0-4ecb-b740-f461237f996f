"use strict";

/**
 * Custom Bundle Linking Plugin - Frontend JS
 *
 * This JavaScript file modifies the Course Bundler "Purchase Bundle" button functionality
 * It intercepts the normal bundle purchase workflow and redirects to WooCommerce cart
 * Similar to how the course linking works but specifically for bundles
 */
(function ($) {
  // Debug flag controlled via WP option passed in global customLinkingData
  var debug = false;
  if ( typeof customLinkingData !== 'undefined' && typeof customLinkingData.console_enabled !== 'undefined' ) {
    debug = !!customLinkingData.console_enabled;
  } else if ( typeof customBundleLinkingData !== 'undefined' && typeof customBundleLinkingData.console_enabled !== 'undefined' ) {
    debug = !!customBundleLinkingData.console_enabled;
  } else if ( typeof customBundleLinkingData !== 'undefined' && typeof customBundleLinkingData.debug !== 'undefined' ) {
    debug = !!customBundleLinkingData.debug;
  }
  // Silence all console output from this script when debug is off
  if ( !debug ) {
    ['log','error','warn','info','debug'].forEach(function(m){
      if (typeof console[m] === 'function') { console[m] = function(){}; }
    });
  }
  /**
   * This block runs when document is ready to ensure our handlers are attached
   * This is crucial for intercepting the click before the original handlers
   */
  $(document).ready(function() {
    console.log('Custom Bundle Linking Plugin: Initializing and looking for bundle purchase buttons...');
    
    // CRITICAL: Check if guest redirect is active (user is not logged in)
    if (typeof clpGuest !== 'undefined' && clpGuest.logged_in === '0') {
      console.log('Guest user detected - guest redirect should handle button clicks, exiting bundle linking');
      return; // Exit early - let guest redirect handle all clicks
    }
    
    // Find and attach our handler to bundle purchase buttons
    attachBundleButtonHandlers();
    
    // Also set up a mutation observer to catch dynamically added buttons
    setupMutationObserver();
  });
  
  /**
   * This function attaches our click handler to all bundle purchase buttons on the page
   * It uses the same selector as the original bundle plugin
   */
  function attachBundleButtonHandlers() {
    // Select all bundle purchase buttons with the data-bundle-id attribute
    $('.mscb-single-bundle__purchase-button').each(function() {
      // Remove any existing click handlers and add our own
      $(this).off('click').on('click', interceptBundleBuyButtonClick);
      console.log('Custom Bundle Linking Plugin: Attached handler to bundle button', $(this).data('bundle-id'));
    });
  }
  
  /**
   * This function sets up a MutationObserver to watch for dynamically added buttons
   * This ensures we catch buttons added after page load
   */
  function setupMutationObserver() {
    // Create an observer instance
    var observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        // If nodes were added to the DOM
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
          // Check for our target buttons and attach handlers
          $(mutation.addedNodes).find('.mscb-single-bundle__purchase-button').each(function() {
            $(this).off('click').on('click', interceptBundleBuyButtonClick);
          });
          
          // Also check if the added node itself is our target
          $(mutation.addedNodes).filter('.mscb-single-bundle__purchase-button').each(function() {
            $(this).off('click').on('click', interceptBundleBuyButtonClick);
          });
        }
      });
    });
    
    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true });
  }
  
  /**
   * Our handler for the Purchase Bundle button
   * This intercepts the click event and redirects to our custom AJAX handler
   * 
   * @param {Event} e - The click event object
   * @returns {boolean} - Always returns false to prevent default action
   */
  function interceptBundleBuyButtonClick(e) {
    // Stop the event from propagating to original handlers
    e.preventDefault();
    e.stopImmediatePropagation();
    
    // Get bundle ID from data attribute
    var bundleId = $(this).data('bundle-id');
    if (!bundleId) {
      console.log('Custom Bundle Linking Plugin: No bundle ID found');
      return false;
    }
    
    console.log('Custom Bundle Linking Plugin: Intercepted click for bundle: ' + bundleId);
    
    // Show loading state on button
    var $button = $(this);
    var originalText = $button.text();
    $button.text('Processing...').addClass('loading').prop('disabled', true);
    
    // Make AJAX call to our custom handler
    $.ajax({
      url: customBundleLinkingData.ajax_url,
      type: 'POST',
      dataType: 'json',
      data: {
        action: 'custom_linking_process_bundle',
        bundle_id: bundleId,
        security: customBundleLinkingData.nonce
      },
      success: function(response) {
        console.log('Custom Bundle Linking Plugin: AJAX response', response);
        
        if (response.success) {
          // Check if product was added to cart or needs product page redirect
          if (response.data.redirect_to === 'product') {
            // Redirect to product page if couldn't add to cart
            console.log('Custom Bundle Linking Plugin: Redirecting to product page');
            window.location.href = response.data.product_url;
          } else {
            // Default: redirect to cart URL
            console.log('Custom Bundle Linking Plugin: Redirecting to cart');
            window.location.href = response.data.cart_url;
          }
        } else {
          // If error, show message and reset button
          alert(response.data.error || 'Error adding bundle to cart. Please try again.');
          $button.text(originalText).removeClass('loading').prop('disabled', false);
        }
      },
      error: function(xhr, status, error) {
        console.error('Custom Bundle Linking Plugin: AJAX error', status, error);
        alert('Error connecting to server. Please try again.');
        $button.text(originalText).removeClass('loading').prop('disabled', false);
      }
    });
    
    return false;
  }
  
  // Log for debugging
  console.log('Custom Bundle Linking Plugin: Frontend JS initialized');
})(jQuery);
