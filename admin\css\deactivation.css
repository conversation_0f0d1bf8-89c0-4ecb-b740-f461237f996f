/**
 * Custom Linking Plugin Deactivation Modal Styles
 */

/* Modal Overlay */
.custom-linking-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.custom-linking-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Modal Content */
.custom-linking-modal-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.custom-linking-modal-overlay.show .custom-linking-modal-content {
    transform: scale(1);
}

/* Modal Header */
.custom-linking-modal-header {
    padding: 20px 25px 15px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.custom-linking-modal-header h2 {
    margin: 0;
    font-size: 18px;
    color: #333;
    font-weight: 600;
}

.custom-linking-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.custom-linking-modal-close:hover {
    background: #e0e0e0;
    color: #333;
}

/* Modal Body */
.custom-linking-modal-body {
    padding: 25px;
    text-align: center;
}

.custom-linking-warning-icon {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.custom-linking-warning-text {
    font-size: 16px;
    color: #666;
    margin: 0 0 20px 0;
    line-height: 1.5;
}

.custom-linking-data-info {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 12px 15px;
    border-radius: 4px;
    margin: 15px 0;
    font-size: 14px;
}

.custom-linking-data-info strong {
    color: #d63384;
    font-weight: bold;
}

.custom-linking-no-data {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 12px 15px;
    border-radius: 4px;
    margin: 15px 0;
    font-size: 14px;
}

/* Export Options */
.custom-linking-export-option {
    text-align: left;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.custom-linking-export-option h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

.custom-linking-radio-option {
    display: block;
    margin: 8px 0;
    cursor: pointer;
    padding: 8px 0;
    font-size: 14px;
    color: #555;
}

.custom-linking-radio-option input[type="radio"] {
    margin-right: 8px;
}

.custom-linking-radio-option:hover {
    color: #333;
}

/* Modal Footer */
.custom-linking-modal-footer {
    padding: 15px 25px 20px;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background: #f8f9fa;
}

.custom-linking-modal-footer .button {
    min-width: 120px;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.custom-linking-cancel-btn {
    background: #6c757d;
    border: 1px solid #6c757d;
    color: #fff;
}

.custom-linking-cancel-btn:hover {
    background: #5a6268;
    border-color: #545b62;
}

.custom-linking-proceed-btn {
    background: #dc3545;
    border: 1px solid #dc3545;
    color: #fff;
}

.custom-linking-proceed-btn:hover {
    background: #c82333;
    border-color: #bd2130;
}

/* Progress Bar */
.custom-linking-progress {
    padding: 30px 25px;
    text-align: center;
}

.custom-linking-progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.custom-linking-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007cba, #0073aa);
    width: 0%;
    transition: width 0.3s ease;
}

.custom-linking-progress-text {
    font-size: 14px;
    color: #666;
    margin: 0;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 600px) {
    .custom-linking-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .custom-linking-modal-header,
    .custom-linking-modal-body,
    .custom-linking-modal-footer {
        padding-left: 20px;
        padding-right: 20px;
    }
    
    .custom-linking-modal-footer {
        flex-direction: column;
    }
    
    .custom-linking-modal-footer .button {
        width: 100%;
        min-width: auto;
    }
}

/* Animation for warning icon */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.custom-linking-warning-icon {
    animation: pulse 2s infinite;
}

/* Loading state */
.custom-linking-proceed-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.custom-linking-proceed-btn:disabled:hover {
    background: #dc3545;
    border-color: #dc3545;
}
