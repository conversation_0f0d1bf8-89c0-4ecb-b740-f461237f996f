<?php
/**
 * Bundle Order Completion Handler for Custom Linking Plugin
 * 
 * This file handles WooCommerce order completion events for bundle purchases
 * and automatically grants access to all courses in the bundle
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

// Include FreeCourse.php to access the comprehensive access check function
$freecourse_path = dirname(__FILE__) . '/FreeCourse.php';
if (file_exists($freecourse_path)) {
    require_once $freecourse_path;
}

/**
 * Process completed bundle purchases and grant course access
 *
 * This function is called after WooCommerce order completion
 * It checks for linked bundles and grants access to all courses within them
 *
 * @param int $order_id The WooCommerce order ID
 * @param int $product_id The product ID that was purchased
 * @param int $user_id The user who made the purchase
 */
function custom_linking_process_bundle_purchase($order_id, $product_id, $user_id) {
    /**
     * This block logs the function call for debugging purposes
     * It records the order, product, and user IDs to track the process
     */
    custom_linking_debug_log("=== BUNDLE PURCHASE PROCESSING START ===");
    custom_linking_debug_log("Processing possible bundle purchase: Order #{$order_id}, Product #{$product_id}, User #{$user_id}");
    custom_linking_debug_log("Timestamp: " . date('Y-m-d H:i:s'));
    
    // Log call stack for debugging (if function exists)
    if (function_exists('wp_debug_backtrace_summary')) {
        custom_linking_debug_log("Call stack: " . wp_debug_backtrace_summary());
    } else {
        custom_linking_debug_log("Function called directly");
    }
    
    /**
     * Find the bundle ID linked to this product
     * We query our custom linking table to find the bundle association
     */
    $bundle_id = custom_linking_get_bundle_by_product($product_id);
    
    /**
     * If a bundle is found, grant access to all courses in it
     * Otherwise, this might be a regular product, not a bundle
     */
    if ($bundle_id) {
        custom_linking_debug_log("Found bundle #{$bundle_id} linked to product #{$product_id}");
        
        // Grant access to all courses in the bundle
        $result = custom_linking_grant_bundle_access($user_id, $bundle_id);
        
        if ($result) {
            custom_linking_debug_log("Successfully granted access to all courses in bundle #{$bundle_id} for user #{$user_id}");
            return true;
        } else {
            custom_linking_debug_log("Failed to grant access to some or all courses in bundle #{$bundle_id}");
            return false;
        }
    } else {
        custom_linking_debug_log("No bundle found for product #{$product_id}");
        return false; // Not a bundle product
    }
}

/**
 * Helper function to get bundle ID linked to a product
 *
 * @param int $product_id The WooCommerce product ID
 * @return int|false The linked bundle ID or false if none found
 */
function custom_linking_get_bundle_by_product($product_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    $bundle_id = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT course_id FROM $table_name WHERE product_id = %d AND type = %s LIMIT 1",
            $product_id,
            'bundle'
        )
    );
    
    return $bundle_id ? (int) $bundle_id : false;
}

/**
 * Helper function to grant access to all courses in a bundle
 * 
 * This function directly uses the Course Bundler's enrollment system
 * to enroll the user in all courses within a bundle
 *
 * @param int $user_id The WordPress user ID
 * @param int $bundle_id The bundle ID
 * @return bool True if access was granted to all courses successfully
 */
function custom_linking_grant_bundle_access($user_id, $bundle_id) {
    /**
     * This block logs the function call for debugging purposes
     * It records which bundle is being processed for which user
     */
    custom_linking_debug_log("Granting bundle access: Bundle #{$bundle_id} for User #{$user_id}");
    
    /**
     * Check if the Course Bundler enrollment class exists
     * This is the preferred method as it uses the bundler's native enrollment system
     */
    if (class_exists('MSCB_Enrollment')) {
        // Create an instance of the enrollment class
        $enrollment = new MSCB_Enrollment();
        
        // Use reflection to access the private method
        $reflection = new ReflectionClass('MSCB_Enrollment');
        $method = $reflection->getMethod('enroll_user_in_bundle');
        $method->setAccessible(true);
        
        // Call the private method to enroll user
        custom_linking_debug_log("Using Course Bundler's native enrollment system for bundle #{$bundle_id}");
        $method->invokeArgs($enrollment, array($user_id, $bundle_id));
        
        // Record bundle completion in the bundler's system too
        if (class_exists('MSCB_Bundle')) {
            $bundle = new MSCB_Bundle();
            $bundle->enroll_user($bundle_id, $user_id);
            custom_linking_debug_log("Recorded bundle enrollment in Course Bundler system for user #{$user_id} in bundle #{$bundle_id}");
        }
        
        return true;
    }
    
    /**
     * Fallback method: manually enroll in each course
     * Only used if the Course Bundler enrollment class isn't available
     */
    custom_linking_debug_log("Fallback: Manual enrollment for bundle #{$bundle_id}");

    // Try to get courses from Course Bundler plugin if available
    $courses = array();

    if (class_exists('MSCB_Bundle')) {
        custom_linking_debug_log("MSCB_Bundle class found, attempting to get bundle courses");
        $bundle = new MSCB_Bundle();
        $courses = $bundle->get_bundle_courses($bundle_id);
    } else {
        custom_linking_debug_log("MSCB_Bundle class not found, trying alternative methods to get bundle courses");

        // Alternative method 1: Check if bundle is a MasterStudy LMS course with bundle meta
        $bundle_courses_meta = get_post_meta($bundle_id, 'stm_lms_bundle_courses', true);
        if (!empty($bundle_courses_meta) && is_array($bundle_courses_meta)) {
            custom_linking_debug_log("Found bundle courses in post meta: " . print_r($bundle_courses_meta, true));
            foreach ($bundle_courses_meta as $course_id) {
                $courses[] = array('course_id' => $course_id);
            }
        }

        // Alternative method 2: Check for custom bundle structure in database
        if (empty($courses)) {
            global $wpdb;
            $bundle_courses_table = $wpdb->prefix . 'stm_lms_bundle_courses';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$bundle_courses_table}'");

            if ($table_exists) {
                custom_linking_debug_log("Found bundle courses table, querying for bundle #{$bundle_id}");
                $course_results = $wpdb->get_results(
                    $wpdb->prepare(
                        "SELECT course_id FROM {$bundle_courses_table} WHERE bundle_id = %d",
                        $bundle_id
                    )
                );

                if (!empty($course_results)) {
                    foreach ($course_results as $course_result) {
                        $courses[] = array('course_id' => $course_result->course_id);
                    }
                    custom_linking_debug_log("Found " . count($courses) . " courses in bundle table");
                }
            }
        }

        /**
         * Alternative method 3: Dynamic course discovery
         * 
         * Previously, this section contained hardcoded course IDs that were causing
         * duplicate course access issues. Now we rely on dynamic discovery methods.
         * 
         * If no courses are found by this point, we'll log it but won't add any
         * hardcoded fallbacks to prevent duplicate course access problems.
         */
        if (empty($courses)) {
            custom_linking_debug_log("No bundle courses found via automatic methods. Proceeding without manual fallbacks.");
            // No hardcoded fallbacks to prevent duplicate course access issues
        }
    }

    if (empty($courses)) {
        custom_linking_debug_log("No courses found in bundle #{$bundle_id} after trying all methods");
        return false;
    }

    custom_linking_debug_log("Found " . count($courses) . " courses in bundle #{$bundle_id}");
    
    // Enhanced logging for bundle processing
    custom_linking_debug_log("=== BUNDLE DUPLICATE PREVENTION SUMMARY ===");
    custom_linking_debug_log("Bundle ID: #{$bundle_id}");
    custom_linking_debug_log("User ID: #{$user_id}");
    custom_linking_debug_log("Total courses found in bundle: " . count($courses));
    custom_linking_debug_log("ENHANCED DUPLICATE PREVENTION: This version includes comprehensive access checking");
    custom_linking_debug_log("DUPLICATE CHECKS: 1) Initial access check, 2) Pre-insertion verification, 3) Fallback verification");
    
    $enrolled_count = 0;
    $skipped_count = 0;
    
    // Process each course using comprehensive duplicate checking
    $success = true;
    foreach ($courses as $course) {
        $course_id = $course['course_id'];
        
        custom_linking_debug_log("Processing course #{$course_id} for user #{$user_id} from bundle #{$bundle_id}");
        
        // COMPREHENSIVE DUPLICATE CHECK: Use the same robust checking as FreeCourse.php
        if (function_exists('custom_linking_user_has_course_access')) {
            if (custom_linking_user_has_course_access($user_id, $course_id)) {
                custom_linking_debug_log("DUPLICATE PREVENTION: User #{$user_id} already has access to course #{$course_id} - skipping to prevent duplicates");
                $skipped_count++; // Increment skipped count
                continue; // Skip this course entirely
            }
        } else {
            // Fallback comprehensive check if FreeCourse function not available
            $already_has_access = custom_linking_comprehensive_access_check($user_id, $course_id);
            if ($already_has_access) {
                custom_linking_debug_log("DUPLICATE PREVENTION: User #{$user_id} already has access to course #{$course_id} via fallback check - skipping");
                $skipped_count++; // Increment skipped count
                continue;
            }
        }
        
        custom_linking_debug_log("User #{$user_id} does NOT have access to course #{$course_id} - proceeding with enrollment");
        
        if (class_exists('STM_LMS_Course')) {
            /**
             * Use the MasterStudy LMS Course enrollment method
             * BUT ensure that the proper timestamp is set to avoid duplicate enrollments
             * Previously, missing proper timestamp caused the WordPress course to appear twice
             * with different dates (current date vs 1970-01-01).
             *
             * Instead of directly using STM_LMS_Course::add_user_course(),
             * we'll first check if the user already has the course in the database table and
             * only add it if needed with a proper timestamp.
             */
            global $wpdb;
            $user_courses_table = $wpdb->prefix . 'stm_lms_user_courses';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$user_courses_table}'");
            
            if ($table_exists) {
                // Final database-level check (this should be redundant now, but kept for safety)
                $existing_access = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT id FROM {$user_courses_table} WHERE user_id = %d AND course_id = %d",
                        $user_id,
                        $course_id
                    )
                );
                
                if (!$existing_access) {
                    // TRIPLE-CHECK: Before inserting, do one final verification
                    // This ensures we absolutely don't create duplicates
                    custom_linking_debug_log("FINAL VERIFICATION: Checking once more before database insertion for course #{$course_id}");
                    
                    // Check one more time using the most comprehensive method
                    $pre_insert_check = false;
                    if (function_exists('custom_linking_user_has_course_access')) {
                        $pre_insert_check = custom_linking_user_has_course_access($user_id, $course_id);
                    } else {
                        $pre_insert_check = custom_linking_comprehensive_access_check($user_id, $course_id);
                    }
                    
                    if ($pre_insert_check) {
                        custom_linking_debug_log("FINAL SAFETY CHECK: User #{$user_id} gained access to course #{$course_id} between checks - aborting insertion");
                        $skipped_count++; // Count this as skipped
                        continue; // Skip to next course
                    }
                    
                    // User doesn't have this course yet, so add it with PROPER TIMESTAMP
                    $insert_result = $wpdb->insert(
                        $user_courses_table,
                        array(
                            'user_id' => $user_id,
                            'course_id' => $course_id,
                            'status' => 'enrolled',
                            'start_time' => time(), // Unix timestamp for consistent date storage
                            'progress_percent' => 0,
                            'current_lesson_id' => 0
                        ),
                        array('%d', '%d', '%s', '%d', '%d', '%d') // Changed %s to %d for timestamp
                    );
                    
                    if ($insert_result !== false) {
                        custom_linking_debug_log("SUCCESS: Enrolled user #{$user_id} in course #{$course_id} from bundle #{$bundle_id} with proper timestamp");
                        $enrolled_count++; // Increment enrolled count
                        
                        // Add student count like the Course Bundler does
                        if (class_exists('STM_LMS_Course') && method_exists('STM_LMS_Course', 'add_student')) {
                            STM_LMS_Course::add_student($course_id);
                        }
                        
                        // Update user meta for compatibility
                        custom_linking_update_user_course_meta($user_id, $course_id);
                        
                    } else {
                        custom_linking_debug_log("ERROR: Failed to insert course #{$course_id} for user #{$user_id}: " . $wpdb->last_error);
                        $success = false;
                    }
                } else {
                    custom_linking_debug_log("FINAL SAFETY CHECK: User #{$user_id} already has database access to course #{$course_id} - skipping");
                    $skipped_count++; // Count this as skipped
                }
                
            } else {
                // Table doesn't exist, fall back to original method
                // But still perform duplicate checking before enrollment
                custom_linking_debug_log("Database table not found, using STM_LMS_Course fallback for course #{$course_id}");
                
                // Even with fallback, check for existing access to prevent duplicates
                $fallback_has_access = false;
                if (function_exists('custom_linking_user_has_course_access')) {
                    $fallback_has_access = custom_linking_user_has_course_access($user_id, $course_id);
                } else {
                    $fallback_has_access = custom_linking_comprehensive_access_check($user_id, $course_id);
                }
                
                if ($fallback_has_access) {
                    custom_linking_debug_log("FALLBACK DUPLICATE CHECK: User #{$user_id} already has access to course #{$course_id} - skipping fallback enrollment");
                    $skipped_count++; // Count this as skipped
                } else {
                    // Safe to proceed with fallback enrollment
                    if (class_exists('STM_LMS_Course') && method_exists('STM_LMS_Course', 'add_user_course')) {
                        STM_LMS_Course::add_user_course(
                            $course_id,
                            $user_id,
                            0, // current_lesson_id
                            0, // progress
                            false, // is_translate
                            '', // enterprise
                            $bundle_id, // bundle_id
                            '', // for_points
                            '' // instructor_id
                        );
                        
                        $enrolled_count++; // Increment enrolled count
                        
                        // Add student count like the Course Bundler does
                        if (method_exists('STM_LMS_Course', 'add_student')) {
                            STM_LMS_Course::add_student($course_id);
                        }
                        
                        // Update user meta for compatibility
                        custom_linking_update_user_course_meta($user_id, $course_id);
                        
                        custom_linking_debug_log("SUCCESS: Enrolled user #{$user_id} in course #{$course_id} from bundle #{$bundle_id} using fallback method");
                    } else {
                        custom_linking_debug_log("ERROR: STM_LMS_Course class or add_user_course method not available for fallback enrollment");
                        $success = false;
                    }
                }
            }
        } else {
            // Ultimate fallback to our custom enrollment
            custom_linking_debug_log("Using ultimate fallback custom enrollment for course #{$course_id}");
            
            // Even in ultimate fallback, check for existing access
            $ultimate_has_access = false;
            if (function_exists('custom_linking_user_has_course_access')) {
                $ultimate_has_access = custom_linking_user_has_course_access($user_id, $course_id);
            } else {
                $ultimate_has_access = custom_linking_comprehensive_access_check($user_id, $course_id);
            }
            
            if ($ultimate_has_access) {
                custom_linking_debug_log("ULTIMATE FALLBACK DUPLICATE CHECK: User #{$user_id} already has access to course #{$course_id} - skipping");
                $skipped_count++; // Count this as skipped
            } else {
                // Safe to proceed with ultimate fallback
                if (function_exists('custom_linking_grant_course_access')) {
                    $result = custom_linking_grant_course_access($user_id, $course_id, 'bundle');
                    if ($result) {
                        $enrolled_count++; // Increment enrolled count
                        custom_linking_debug_log("SUCCESS: Enrolled user #{$user_id} in course #{$course_id} from bundle #{$bundle_id} using ultimate fallback");
                    } else {
                        custom_linking_debug_log("ERROR: Failed to grant access to course #{$course_id} from bundle #{$bundle_id} using ultimate fallback");
                        $success = false;
                    }
                } else {
                    custom_linking_debug_log("ERROR: Cannot find any enrollment function for course #{$course_id}");
                    $success = false;
                }
            }
        }
    }
    
    // Also record the enrollment in the bundle system if available
    if ($success && class_exists('MSCB_Bundle')) {
        $bundle->enroll_user($bundle_id, $user_id);
    }
    
    // Log the summary of enrollments and skips
    custom_linking_debug_log("=== BUNDLE PROCESSING SUMMARY ===");
    custom_linking_debug_log("Bundle ID: #{$bundle_id}");
    custom_linking_debug_log("User ID: #{$user_id}");
    custom_linking_debug_log("Total courses processed: " . count($courses));
    custom_linking_debug_log("Courses successfully enrolled: {$enrolled_count}");
    custom_linking_debug_log("Courses skipped (already enrolled): {$skipped_count}");
    custom_linking_debug_log("Duplicate prevention: " . ($skipped_count > 0 ? "ACTIVE - prevented {$skipped_count} duplicate(s)" : "NO DUPLICATES FOUND"));
    custom_linking_debug_log("Overall success: " . ($success ? "YES" : "NO"));
    custom_linking_debug_log("=== BUNDLE PURCHASE PROCESSING END ===");
    
    return $success;
}

/**
 * Update user course meta for compatibility
 * This ensures proper meta data is set when enrolling users in courses
 * 
 * @param int $user_id User ID
 * @param int $course_id Course ID
 */
function custom_linking_update_user_course_meta($user_id, $course_id) {
    // Update user meta to indicate course enrollment
    if (function_exists('update_user_meta')) {
        $user_courses = get_user_meta($user_id, 'stm_lms_courses', true);
        if (!is_array($user_courses)) {
            $user_courses = array();
        }
        
        if (!in_array($course_id, $user_courses)) {
            $user_courses[] = $course_id;
            update_user_meta($user_id, 'stm_lms_courses', $user_courses);
        }
        
        // Also set course-specific enrollment meta
        update_user_meta($user_id, "course_enrolled_{$course_id}", time());
        
        custom_linking_debug_log("Updated user meta for user #{$user_id} and course #{$course_id}");
    }
}

/**
 * Comprehensive access check fallback function
 * This is used when the main access function from FreeCourse.php is not available
 * Enhanced to match all the access checks from custom_linking_user_has_course_access
 * 
 * @param int $user_id User ID
 * @param int $course_id Course ID
 * @return bool True if user has access, false otherwise
 */
function custom_linking_comprehensive_access_check($user_id, $course_id) {
    custom_linking_debug_log("Comprehensive access check for user {$user_id} to course {$course_id}");
    
    // Strategy 1: Check MasterStudy LMS user course access function
    if (function_exists('stm_lms_has_course_access')) {
        $has_access = stm_lms_has_course_access($course_id, $user_id);
        if ($has_access) {
            custom_linking_debug_log("Access granted via MasterStudy LMS function");
            return true;
        }
    }
    
    // Strategy 2: Check if user purchased the course through WooCommerce
    if (function_exists('wc_customer_bought_product')) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'linking_table';
        
        // Get linked product for this course
        $product_id = $wpdb->get_var($wpdb->prepare(
            "SELECT product_id FROM {$table_name} WHERE course_id = %d",
            $course_id
        ));
        
        if ($product_id && wc_customer_bought_product('', $user_id, $product_id)) {
            custom_linking_debug_log("Access granted via WooCommerce purchase of product {$product_id}");
            return true;
        }
    }
    
    // Strategy 3: Check user meta 'stm_lms_course_passed'
    $user_courses = get_user_meta($user_id, 'stm_lms_course_passed', true);
    if (is_array($user_courses) && in_array($course_id, $user_courses)) {
        custom_linking_debug_log("Access granted via 'stm_lms_course_passed' meta");
        return true;
    }
    
    // Strategy 4: Check user meta 'stm_lms_user_courses'
    $user_courses_alt = get_user_meta($user_id, 'stm_lms_user_courses', true);
    if (is_array($user_courses_alt) && in_array($course_id, $user_courses_alt)) {
        custom_linking_debug_log("Access granted via 'stm_lms_user_courses' meta");
        return true;
    }
    
    // Strategy 5: Check course-specific enrollment meta
    $course_enrolled = get_user_meta($user_id, "stm_lms_course_{$course_id}_enrolled", true);
    if (!empty($course_enrolled)) {
        custom_linking_debug_log("Access granted via course-specific enrollment meta");
        return true;
    }
    
    // Strategy 6: Check user capabilities
    if (function_exists('user_can') && user_can($user_id, "access_course_{$course_id}")) {
        custom_linking_debug_log("Access granted via user capability");
        return true;
    }
    
    // Strategy 7: Direct database check for enrollment
    global $wpdb;
    $user_courses_table = $wpdb->prefix . 'stm_lms_user_courses';
    
    // Check if the table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$user_courses_table}'") == $user_courses_table;
    
    if ($table_exists) {
        $existing_access = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM {$user_courses_table} WHERE user_id = %d AND course_id = %d",
                $user_id,
                $course_id
            )
        );
        
        if ($existing_access) {
            custom_linking_debug_log("Access granted via direct database check");
            return true;
        }
    }
    
    // Strategy 8: Check course students meta
    $course_students = get_post_meta($course_id, 'stm_lms_course_students', true);
    if (is_array($course_students) && in_array($user_id, $course_students)) {
        custom_linking_debug_log("Access granted via course students meta");
        return true;
    }
    
    custom_linking_debug_log("User {$user_id} does NOT have access to course {$course_id}");
    return false;
}

/**
 * Hook into WooCommerce order processing
 * This attaches our bundle processor to the order completion pipeline
 */
function custom_linking_init_bundle_purchase_hook() {
    // Hook into the custom linking order processing
    add_action('custom_linking_process_product_purchase', 'custom_linking_process_bundle_purchase', 10, 3);
}
add_action('init', 'custom_linking_init_bundle_purchase_hook');
