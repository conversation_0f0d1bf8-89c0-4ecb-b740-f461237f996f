<?php
/**
 * Integration Helper for Custom Linking Plugin
 * 
 * This file ensures all components of the plugin work together seamlessly
 * It provides shared functions and handles the interaction between different modules
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize plugin integration
 * This function sets up the integration between different modules
 */
function custom_linking_init_integration() {
    // Ensure proper loading order
    add_action('init', 'custom_linking_setup_integration_hooks', 1);
    
    // Add shared filters and actions
    add_action('wp_loaded', 'custom_linking_register_shared_hooks');
}

/**
 * Setup integration hooks
 */
function custom_linking_setup_integration_hooks() {
    custom_linking_debug_log('Setting up integration hooks');
    
    // Add filter for course processing pipeline
    // This allows different modules to intercept course processing
    add_filter('custom_linking_before_course_process', 'custom_linking_integration_filter', 1, 2);
    
    // Add action after course processing
    add_action('custom_linking_after_course_process', 'custom_linking_integration_cleanup', 10, 2);
}

/**
 * Register shared hooks that all modules can use
 */
function custom_linking_register_shared_hooks() {
    custom_linking_debug_log('Registering shared hooks for all modules');
    
    // Shared action for when any course access is granted
    do_action('custom_linking_init_shared_hooks');
}

/**
 * Integration filter for course processing
 * This allows modules to modify or intercept course processing
 * 
 * @param array $response Current response
 * @param int $course_id Course ID being processed
 * @return array Modified response
 */
function custom_linking_integration_filter($response, $course_id) {
    custom_linking_debug_log("Integration filter called for course {$course_id}");
    
    // Allow other modules to process first
    // The response will be passed through the filter chain
    return $response;
}

/**
 * Integration cleanup after course processing
 * 
 * @param array $response The final response
 * @param int $course_id Course ID that was processed
 */
function custom_linking_integration_cleanup($response, $course_id) {
    custom_linking_debug_log("Course processing completed for course {$course_id}");
    
    // Log final result for debugging
    if (isset($response['success'])) {
        $status = $response['success'] ? 'SUCCESS' : 'FAILED';
        custom_linking_debug_log("Final course processing status: {$status}");
    }
    
    // Clear any temporary data or caches if needed
    wp_cache_delete("course_processing_{$course_id}", 'custom_linking');
}

/**
 * Shared utility function to check if a module is available
 * 
 * @param string $module_name Name of the module to check
 * @return bool True if module is available
 */
function custom_linking_is_module_available($module_name) {
    $modules = array(
        'free_course' => 'custom_linking_is_course_free',
        'bundle' => 'custom_linking_enqueue_bundle_scripts',
        'guest_login' => 'custom_linking_init_guest_login',
        'main_frontend' => 'custom_linking_init_frontend'
    );
    
    if (isset($modules[$module_name])) {
        return function_exists($modules[$module_name]);
    }
    
    return false;
}

/**
 * Shared function to get course URL with proper handling
 * 
 * @param int $course_id Course ID
 * @return string Course URL
 */
function custom_linking_get_course_url($course_id) {
    if (!$course_id) {
        return '';
    }
    
    // Use WordPress function to get permalink
    $course_url = get_permalink($course_id);
    
    // Fallback if get_permalink fails
    if (!$course_url || $course_url === false) {
        $course_url = home_url("/course/{$course_id}/");
    }
    
    custom_linking_debug_log("Generated course URL for course {$course_id}: {$course_url}");
    
    return $course_url;
}

/**
 * Shared function to determine if user needs login
 * 
 * @return array|false Login info array or false if not needed
 */
function custom_linking_get_login_info() {
    if (is_user_logged_in()) {
        return false;
    }
    
    // Get current page URL for return
    $current_url = '';
    if (isset($_SERVER['REQUEST_URI'])) {
        $current_url = home_url($_SERVER['REQUEST_URI']);
    }
    
    return array(
        'needed' => true,
        'login_url' => wp_login_url($current_url),
        'register_url' => wp_registration_url(),
        'current_url' => $current_url
    );
}

/**
 * Shared validation function for course IDs
 * 
 * @param mixed $course_id Course ID to validate
 * @return int|false Valid course ID or false
 */
function custom_linking_validate_course_id($course_id) {
    $course_id = intval($course_id);
    
    if ($course_id <= 0) {
        return false;
    }
    
    // Check if course exists
    $course = get_post($course_id);
    if (!$course || $course->post_type !== 'stm-courses') {
        custom_linking_debug_log("Invalid course ID: {$course_id} - course not found or wrong post type");
        return false;
    }
    
    return $course_id;
}

/**
 * Shared nonce validation function
 * 
 * @param string $nonce Nonce to validate
 * @param string $action Nonce action
 * @return bool True if valid
 */
function custom_linking_validate_nonce($nonce, $action = 'custom_linking_nonce') {
    if (empty($nonce)) {
        custom_linking_debug_log('Nonce validation failed: empty nonce');
        return false;
    }
    
    if (!wp_verify_nonce($nonce, $action)) {
        custom_linking_debug_log('Nonce validation failed: invalid nonce');
        return false;
    }
    
    return true;
}

// Initialize integration
custom_linking_init_integration();
