# Shortcode Documentation - Vedmg-woo-LMS Plugin

## 📚 What are Shortcodes?

Shortcodes are simple text commands that you can add to your WordPress pages or posts to display special content. Think of them like magic words that turn into └── .vedmg-nav-btn               # Individual button styling
    ├── .vedmg-home-btn          # Home button variant
    ├── .vedmg-courses-btn       # Courses button variant
    └── .vedmg-cart-btn          # Cart button varianttons, forms, or other features when your page loads.

**Example:** When you type `[linking]` in your page, it automatically creates navigation buttons for your website.

---

## 🎯 Available Shortcodes

### 1. **`[linking]` - All Navigation Buttons**

**What it does:** Creates three buttons - Home Page, Enrolled Courses, and Cart (cart only shows for logged-in users)

**How to use:** Simply type `[linking]` anywhere in your page or post

**What visitors see:**
- A green "Go to Home Page" button
- A blue "Go to Enrolled Courses" button  
- A red "Go to Cart" button (only for logged-in users)
- All buttons are displayed side by side in a nice container

---

### 2. **`[linking-home]` - Home Page Button Only**

**What it does:** Creates only the Home Page button

**How to use:** Type `[linking-home]` in your content

**What visitors see:**
- A single green "Go to Home Page" button
- Button is centered on the page
- No container background - just the button

**Positioning Options:**
- `[linking-home]` - Button appears in the center (default)
- `[linking-home align="left"]` - Button appears on the left
- `[linking-home align="right"]` - Button appears on the right

---

### 3. **`[linking-enrolled]` - Enrolled Courses Button Only**

**What it does:** Creates only the Enrolled Courses button

**How to use:** Type `[linking-enrolled]` in your content

**What visitors see:**
- A single blue "Go to Enrolled Courses" button
- Button is centered on the page
- No container background - just the button

**Positioning Options:**
- `[linking-enrolled]` - Button appears in the center (default)
- `[linking-enrolled align="left"]` - Button appears on the left
- `[linking-enrolled align="right"]` - Button appears on the right

---

### 4. **`[linking-cart]` - Cart Button Only (Logged-in Users Only)**

**What it does:** Creates only the Cart button

**How to use:** Type `[linking-cart]` in your content

**⚠️ Important:** This button only appears for logged-in users. Non-logged-in users see nothing at all.

**What logged-in visitors see:**
- A single red "Go to Cart" button
- Button is centered on the page
- No container background - just the button

**What non-logged-in visitors see:**
- Nothing (completely invisible)

**Positioning Options:**
- `[linking-cart]` - Button appears in the center (default)
- `[linking-cart align="left"]` - Button appears on the left
- `[linking-cart align="right"]` - Button appears on the right

---

## 🎨 Button Colors and Styles

### Home Page Button
- **Color:** Green (#28a745)
- **Text:** "Go to Home Page"
- **Hover Effect:** Darker green with subtle lift animation

### Enrolled Courses Button
- **Color:** Blue (#007cba)
- **Text:** "Go to Enrolled Courses"
- **Hover Effect:** Darker blue with subtle lift animation

### Cart Button (Logged-in Users Only)
- **Color:** Red (#e74c3c)
- **Text:** "Go to Cart"
- **Hover Effect:** Darker red with subtle lift animation
- **Visibility:** Only appears for logged-in users

### Cart Button
- **Color:** Red (#dc3545)
- **Text:** "Go to Cart"
- **Hover Effect:** Darker red with subtle lift animation

### Button Features
- ✅ **Responsive:** Buttons adapt to mobile screens
- ✅ **Accessible:** Keyboard navigation friendly
- ✅ **Smooth Animations:** Hover and click effects
- ✅ **Professional Design:** Modern, clean appearance

---

## 📱 Mobile-Friendly Design

On mobile devices:
- Buttons automatically stack vertically for `[linking]`
- Single buttons remain centered and full-width
- Touch-friendly button sizes
- Optimized spacing for thumbs

---

## 🔧 Where the Buttons Lead

### Home Page Button
- **Destination:** Your website's main homepage
- **URL:** `http://yoursite.com/` (automatically detects your domain)

### Enrolled Courses Button
- **Destination:** User's enrolled courses page
- **URL:** `http://yoursite.com/user-account/enrolled-courses/`

### Cart Button (Logged-in Users Only)
- **Destination:** WooCommerce shopping cart page
- **URL:** `http://yoursite.com/cart/`

### Cart Button
- **Destination:** User's cart page
- **URL:** `http://yoursite.com/cart/` (only for logged-in users)

**Note:** URLs are automatically generated based on your website's address, so they work on any domain (localhost, staging, or live site).

---

## 💡 Common Use Cases

### For Course Landing Pages
```
Welcome to our course! Ready to get started?

[linking-enrolled]

Or go back to explore more courses:

[linking-home align="center"]
```

### For Course Purchase Pages
```
Ready to add this course to your cart?

[linking-cart]

Or continue browsing:

[linking-home align="center"]
```

### For Member Dashboard
```
Welcome back! Quick navigation:

[linking]
```

### For Thank You Pages
```
Thank you for your purchase!

[linking]
```

### For Navigation Sections
```
Quick Navigation:

[linking-home align="left"]
[linking-enrolled align="right"]
```

---

## 🛠️ Technical Details (For Engineers)

### File Structure
```
/shortcode/
├── shortcode.php       # Main shortcode registration and handlers
├── css/shortcode.css   # Styling for all shortcode outputs
└── js/shortcode.js     # Vanilla JavaScript functionality
```

### PHP Implementation

#### Shortcode Registration
```php
add_shortcode('linking', 'custom_linking_shortcode_handler');
add_shortcode('linking-home', 'custom_linking_home_shortcode_handler');
add_shortcode('linking-enrolled', 'custom_linking_enrolled_shortcode_handler');
add_shortcode('linking-cart', 'custom_linking_cart_shortcode_handler');
add_shortcode('linking-cart', 'custom_linking_cart_shortcode_handler');
```

#### Asset Management
- **Conditional Loading:** CSS/JS only enqueued when shortcode is detected on page
- **WordPress Standards:** Uses `wp_enqueue_style()` and `wp_enqueue_script()`
- **Version Control:** Assets versioned with plugin version for cache busting

#### URL Generation
```php
$home_url = home_url('/');
$enrolled_courses_url = home_url('/user-account/enrolled-courses/');
$cart_url = home_url('/cart/');
```
- **Dynamic URLs:** Uses WordPress `home_url()` for environment portability
- **XSS Protection:** All URLs escaped with `esc_url()`

### CSS Architecture

#### Class Structure
```css
.vedmg-linking-container          # Main container
├── .single-button                # Modifier for individual buttons
├── .align-left                   # Left alignment modifier
└── .align-right                  # Right alignment modifier

.vedmg-navigation-buttons         # Button container
├── .single-button                # Single button layout
└── .vedmg-nav-btn               # Individual button styling
    ├── .vedmg-home-btn          # Home button variant
    ├── .vedmg-courses-btn       # Courses button variant
    └── .vedmg-cart-btn          # Cart button variant
```

#### Responsive Breakpoints
- **768px:** Buttons stack vertically, full-width mobile optimization
- **480px:** Reduced padding and font sizes for small screens

### JavaScript Implementation

#### Vanilla JS Architecture
```javascript
document.addEventListener('DOMContentLoaded', function() {
    // Event delegation for button clicks
    // Visual feedback animations
    // Console logging for debugging
});
```

#### Features
- **No jQuery Dependency:** Pure vanilla JavaScript
- **Event Delegation:** Efficient event handling
- **Progressive Enhancement:** Works without JavaScript
- **Debug Logging:** Configurable console output

### WordPress Integration

#### Hook Priority
- **Asset Enqueuing:** `wp_enqueue_scripts` action
- **Shortcode Detection:** Uses `has_shortcode()` for conditional loading
- **Initialization:** `init` action with priority 10

#### Security Features
- **Nonce Protection:** CSRF tokens for AJAX requests
- **Data Sanitization:** All attributes sanitized with `shortcode_atts()`
- **Output Escaping:** XSS prevention with `esc_attr()`, `esc_url()`, `esc_html()`

### Performance Considerations

#### Optimization Strategies
- **Conditional Loading:** Assets only load when needed
- **Minimal DOM Manipulation:** Efficient JavaScript execution
- **CSS Specificity:** Minimal specificity conflicts
- **Mobile-First Design:** Optimized for performance on mobile devices

#### Caching Compatibility
- **Version Management:** Plugin version used for cache busting
- **Static Assets:** CSS/JS are cacheable static files
- **No Database Queries:** Shortcodes don't perform additional DB calls

### Extensibility

#### Adding New Shortcodes
1. Register shortcode: `add_shortcode('new-shortcode', 'handler_function')`
2. Create handler function with `shortcode_atts()` parsing
3. Add to asset detection in `custom_linking_enqueue_shortcode_assets()`
4. Add corresponding CSS classes and JavaScript handlers

#### Customization Points
- **Button Text:** Modify in shortcode handler functions
- **URLs:** Update `home_url()` calls for different destinations
- **Styling:** Override CSS classes with higher specificity
- **Alignment Options:** Extend `align` attribute with new values

### Browser Support
- **Modern Browsers:** Full support for all features
- **IE11+:** Basic functionality (no CSS Grid, uses Flexbox fallback)
- **Mobile Browsers:** Optimized touch interactions
- **Accessibility:** WCAG 2.1 AA compliant with keyboard navigation

---

## 📝 Changelog

### Version 1.0.0
- ✅ Initial shortcode implementation
- ✅ Four shortcode variations (`linking`, `linking-home`, `linking-enrolled`, `linking-cart`)
- ✅ Responsive design with mobile optimization
- ✅ Alignment options for single buttons
- ✅ Vanilla JavaScript implementation
- ✅ WordPress security best practices
- ✅ Clean, minimal styling for single buttons
- ✅ Dynamic URL generation for environment portability
- ✅ User authentication for cart functionality (logged-in users only)

---

## 🎉 Quick Start

1. **Add to any page/post:** Type one of the shortcodes in your content
2. **Save/Update:** Publish your page
3. **View result:** See the buttons appear on your live page
4. **Test functionality:** Click buttons to verify navigation works

That's it! Your navigation buttons are ready to use. 🚀
