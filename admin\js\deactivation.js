/**
 * Custom Linking Plugin Deactivation Handler
 * 
 * Handles the deactivation confirmation dialog with optional data export
 */

(function($) {
    'use strict';
    
    var deactivationModal = null;
    var isExporting = false;
    var isDeactivating = false;
    
    $(document).ready(function() {
        initDeactivationHandler();
    });
    
    /**
     * Initialize the deactivation confirmation system
     */
    function initDeactivationHandler() {
        // Find the deactivate link for our plugin
        var deactivateLink = $('tr[data-plugin="' + customLinkingDeactivation.pluginFile + '"] .deactivate a');
        
        if (deactivateLink.length === 0) {
            // Fallback: look for any link containing our plugin name
            deactivateLink = $('a[href*="custom-linking-plugin"]').filter(function() {
                return $(this).text().toLowerCase().indexOf('deactivate') !== -1;
            });
        }
        
        if (deactivateLink.length > 0) {
            // Intercept the deactivate click
            deactivateLink.on('click', function(e) {
                e.preventDefault();
                showDeactivationModal($(this).attr('href'));
            });
        }
    }
    
    /**
     * Show the deactivation confirmation modal
     */
    function showDeactivationModal(deactivateUrl) {
        // First, get the data count
        getDataCount(function(count) {
            createAndShowModal(deactivateUrl, count);
        });
    }
    
    /**
     * Get the count of records that will be deleted
     */
    function getDataCount(callback) {
        $.ajax({
            url: customLinkingDeactivation.ajaxurl,
            type: 'POST',
            data: {
                action: 'custom_linking_get_data_count',
                nonce: customLinkingDeactivation.nonce
            },
            success: function(response) {
                if (response.success) {
                    callback(response.data.count);
                } else {
                    callback(0);
                }
            },
            error: function() {
                callback(0);
            }
        });
    }
    
    /**
     * Create and show the confirmation modal
     */
    function createAndShowModal(deactivateUrl, dataCount) {
        var strings = customLinkingDeactivation.strings;
        
        // Create modal HTML
        var modalHtml = '<div id="custom-linking-deactivation-modal" class="custom-linking-modal-overlay">' +
            '<div class="custom-linking-modal-content">' +
                '<div class="custom-linking-modal-header">' +
                    '<h2>' + strings.confirmTitle + '</h2>' +
                    '<button type="button" class="custom-linking-modal-close">&times;</button>' +
                '</div>' +
                '<div class="custom-linking-modal-body">' +
                    '<div class="custom-linking-warning-icon">⚠️</div>' +
                    '<p class="custom-linking-warning-text">' + strings.confirmMessage + '</p>';
        
        if (dataCount > 0) {
            modalHtml += '<p class="custom-linking-data-info"><strong>' + dataCount + '</strong> ' + strings.dataCount + '</p>' +
                '<div class="custom-linking-export-option">' +
                    '<h4>' + strings.exportOption + '</h4>' +
                    '<label class="custom-linking-radio-option">' +
                        '<input type="radio" name="export_option" value="yes" checked>' +
                        '<span>' + strings.exportYes + '</span>' +
                    '</label>' +
                    '<label class="custom-linking-radio-option">' +
                        '<input type="radio" name="export_option" value="no">' +
                        '<span>' + strings.exportNo + '</span>' +
                    '</label>' +
                '</div>';
        } else {
            modalHtml += '<p class="custom-linking-no-data">' + strings.noDataFound + '</p>';
        }
        
        modalHtml += '</div>' +
                '<div class="custom-linking-modal-footer">' +
                    '<button type="button" class="button custom-linking-cancel-btn">' + strings.cancelButton + '</button>' +
                    '<button type="button" class="button-primary custom-linking-proceed-btn">' + strings.deactivateButton + '</button>' +
                '</div>' +
                '<div class="custom-linking-progress" style="display: none;">' +
                    '<div class="custom-linking-progress-bar">' +
                        '<div class="custom-linking-progress-fill"></div>' +
                    '</div>' +
                    '<p class="custom-linking-progress-text">Processing...</p>' +
                '</div>' +
            '</div>' +
        '</div>';
        
        // Add modal to page
        $('body').append(modalHtml);
        deactivationModal = $('#custom-linking-deactivation-modal');
        
        // Show modal with animation
        setTimeout(function() {
            deactivationModal.addClass('show');
        }, 10);
        
        // Bind events
        bindModalEvents(deactivateUrl, dataCount);
    }
    
    /**
     * Bind modal events
     */
    function bindModalEvents(deactivateUrl, dataCount) {
        // Close modal events
        deactivationModal.find('.custom-linking-modal-close, .custom-linking-cancel-btn').on('click', function() {
            closeModal();
        });
        
        // Close on overlay click
        deactivationModal.on('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        // Proceed button
        deactivationModal.find('.custom-linking-proceed-btn').on('click', function() {
            if (isExporting || isDeactivating) {
                return;
            }
            
            var shouldExport = dataCount > 0 && deactivationModal.find('input[name="export_option"]:checked').val() === 'yes';
            
            if (shouldExport) {
                exportDataThenDeactivate(deactivateUrl);
            } else {
                proceedWithDeactivation(deactivateUrl);
            }
        });
        
        // Prevent closing during processing
        $(document).on('keydown.custom-linking-modal', function(e) {
            if (e.keyCode === 27 && (isExporting || isDeactivating)) { // ESC key
                e.preventDefault();
            } else if (e.keyCode === 27) {
                closeModal();
            }
        });
    }
    
    /**
     * Export data then deactivate
     */
    function exportDataThenDeactivate(deactivateUrl) {
        isExporting = true;
        showProgress(customLinkingDeactivation.strings.exportingMessage);
        
        $.ajax({
            url: customLinkingDeactivation.ajaxurl,
            type: 'POST',
            data: {
                action: 'custom_linking_export_data',
                nonce: customLinkingDeactivation.nonce
            },
            success: function(response) {
                isExporting = false;
                
                if (response.success) {
                    // Download the file
                    downloadFile(response.url, response.filename);
                    
                    // Show success message briefly then proceed
                    showProgress(customLinkingDeactivation.strings.exportSuccess);
                    
                    setTimeout(function() {
                        proceedWithDeactivation(deactivateUrl);
                    }, 2000);
                    
                } else {
                    // Export failed, ask if user wants to proceed anyway
                    hideProgress();
                    if (confirm(customLinkingDeactivation.strings.exportFailed)) {
                        proceedWithDeactivation(deactivateUrl);
                    }
                }
            },
            error: function() {
                isExporting = false;
                hideProgress();
                
                if (confirm(customLinkingDeactivation.strings.exportFailed)) {
                    proceedWithDeactivation(deactivateUrl);
                }
            }
        });
    }
    
    /**
     * Proceed with plugin deactivation
     */
    function proceedWithDeactivation(deactivateUrl) {
        isDeactivating = true;
        showProgress(customLinkingDeactivation.strings.deactivatingMessage);
        
        // Proceed with deactivation
        setTimeout(function() {
            window.location.href = deactivateUrl;
        }, 1000);
    }
    
    /**
     * Download a file
     */
    function downloadFile(url, filename) {
        var link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    /**
     * Show progress bar
     */
    function showProgress(message) {
        var progress = deactivationModal.find('.custom-linking-progress');
        var progressText = deactivationModal.find('.custom-linking-progress-text');
        var footer = deactivationModal.find('.custom-linking-modal-footer');
        var body = deactivationModal.find('.custom-linking-modal-body');
        
        progressText.text(message);
        body.hide();
        footer.hide();
        progress.show();
        
        // Animate progress bar
        progress.find('.custom-linking-progress-fill').css('width', '0%').animate({
            width: '100%'
        }, 3000);
    }
    
    /**
     * Hide progress bar
     */
    function hideProgress() {
        var progress = deactivationModal.find('.custom-linking-progress');
        var footer = deactivationModal.find('.custom-linking-modal-footer');
        var body = deactivationModal.find('.custom-linking-modal-body');
        
        progress.hide();
        body.show();
        footer.show();
    }
    
    /**
     * Close the modal
     */
    function closeModal() {
        if (isExporting || isDeactivating) {
            return;
        }
        
        if (deactivationModal) {
            deactivationModal.removeClass('show');
            setTimeout(function() {
                deactivationModal.remove();
                deactivationModal = null;
            }, 300);
        }
        
        // Remove keydown event listener
        $(document).off('keydown.custom-linking-modal');
    }
    
})(jQuery);
