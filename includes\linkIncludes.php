<?php
/**
 * Common utility functions for Custom Linking Plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * The following three helper functions were previously unused across the plugin
 * codebase. They have been removed to avoid unnecessary memory usage and to
 * keep the plugin footprint minimal.  Their responsibilities were limited to
 * generic debug logging and plugin-active checks which are already covered by
 * WordPress core helpers elsewhere.  Should a future feature need them again
 * they can easily be reinstated from version control.  The only retained
 * helper here is `custom_linking_sanitize_type()` which IS consumed by
 * `database/linkDatabase.php`. 
 */
// -------------  All former unused helpers have been purged  ------------- //

/**
 * Sanitize and validate type field
 * 
 * @param string $type The type to sanitize
 * @return string Sanitized type
 */
function custom_linking_sanitize_type($type) {
    $type = sanitize_text_field($type);
    return in_array($type, array('course', 'bundle')) ? $type : 'course';
}