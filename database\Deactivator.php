<?php
/**
 * <PERSON>les plugin deactivation tasks
 *
 * This file contains comprehensive deactivation logic
 * that completely removes all traces of the plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

/**
 * Deactivator class for Custom Linking Plugin
 * 
 * Handles complete plugin cleanup with optional data export
 */
class Custom_Linking_Deactivator {
    
    /**
     * Handle plugin deactivation with user confirmation and optional data export
     */
    public static function deactivate() {
        // Check if this is coming from the admin interface
        if (!is_admin()) {
            return;
        }
        
        // Set up the deactivation process
        self::handle_deactivation_process();
    }
    
    /**
     * Handle the complete deactivation process
     */
    private static function handle_deactivation_process() {
        try {
            // Always perform cleanup - the export option will be handled via AJAX
            // before this deactivation hook is called
            self::perform_complete_cleanup();
            
        } catch (Exception $e) {
            // Log error but continue with cleanup
            error_log('Custom Linking Plugin Deactivation Error: ' . $e->getMessage());
            // Still attempt basic cleanup
            self::perform_basic_cleanup();
        }
    }
    
    /**
     * Perform complete cleanup of all plugin data
     */
    private static function perform_complete_cleanup() {
        global $wpdb;
        
        // 1. Drop custom database table
        $table_name = $wpdb->prefix . 'linking_table';
        $wpdb->query("DROP TABLE IF EXISTS {$table_name}");
        
        // 2. Remove all WordPress options created by the plugin
        delete_option('custom_linking_enable_console');
        delete_option('custom_linking_enable_debug_log');
        delete_option('custom_linking_plugin_version');
        
        // 3. Remove debug log file
        self::remove_debug_log();
        
        // 4. Clear any cached data
        wp_cache_flush();
        
        // 5. Remove any temporary files or directories
        self::cleanup_temporary_files();
    }
    
    /**
     * Perform basic cleanup (fallback)
     */
    private static function perform_basic_cleanup() {
        global $wpdb;
        
        try {
            // At minimum, drop the database table
            $table_name = $wpdb->prefix . 'linking_table';
            $wpdb->query("DROP TABLE IF EXISTS {$table_name}");
            
            // Remove core options
            delete_option('custom_linking_enable_console');
            delete_option('custom_linking_enable_debug_log');
            delete_option('custom_linking_plugin_version');
            
        } catch (Exception $e) {
            error_log('Custom Linking Plugin Basic Cleanup Error: ' . $e->getMessage());
        }
    }
    
    /**
     * Remove debug log file
     */
    private static function remove_debug_log() {
        $debug_log_path = dirname(dirname(__FILE__)) . '/debug.log';
        if (file_exists($debug_log_path)) {
            @unlink($debug_log_path);
        }
    }
    
    /**
     * Clean up any temporary files
     */
    private static function cleanup_temporary_files() {
        $plugin_dir = dirname(dirname(__FILE__));
        
        // Remove any .tmp files
        $temp_files = glob($plugin_dir . '/*.tmp');
        if ($temp_files) {
            foreach ($temp_files as $temp_file) {
                @unlink($temp_file);
            }
        }
        
        // Remove any export files older than 1 hour
        $export_files = glob($plugin_dir . '/exports/*.csv');
        if ($export_files) {
            foreach ($export_files as $export_file) {
                if (file_exists($export_file) && (time() - filemtime($export_file)) > 3600) {
                    @unlink($export_file);
                }
            }
        }
    }
    
    /**
     * Export linking data to CSV format
     * This method is called via AJAX before deactivation
     */
    public static function export_data_to_csv() {
        global $wpdb;
        
        try {
            $table_name = $wpdb->prefix . 'linking_table';
            
            // Check if table exists
            if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
                return array(
                    'success' => false,
                    'message' => 'No data table found to export.'
                );
            }
            
            // Get all linking data with course and product details
            $query = "
                SELECT 
                    l.id as link_id,
                    l.course_id,
                    l.product_id,
                    l.type,
                    COALESCE(c.post_title, 'Course Not Found') as course_title,
                    COALESCE(p.post_title, 'Product Not Found') as product_title,
                    COALESCE(c.post_status, 'unknown') as course_status,
                    COALESCE(p.post_status, 'unknown') as product_status
                FROM {$table_name} l
                LEFT JOIN {$wpdb->posts} c ON l.course_id = c.ID
                LEFT JOIN {$wpdb->posts} p ON l.product_id = p.ID
                ORDER BY l.id ASC
            ";
            
            $results = $wpdb->get_results($query, ARRAY_A);
            
            if (empty($results)) {
                return array(
                    'success' => false,
                    'message' => 'No linking data found to export.'
                );
            }
            
            // Create exports directory if it doesn't exist
            $plugin_dir = dirname(dirname(__FILE__));
            $exports_dir = $plugin_dir . '/exports';
            if (!file_exists($exports_dir)) {
                wp_mkdir_p($exports_dir);
            }
            
            // Generate filename with timestamp
            $filename = 'custom-linking-export-' . date('Y-m-d-H-i-s') . '.csv';
            $filepath = $exports_dir . '/' . $filename;
            
            // Create CSV content
            $csv_content = self::generate_csv_content($results);
            
            // Write to file
            $file_written = file_put_contents($filepath, $csv_content);
            
            if ($file_written === false) {
                return array(
                    'success' => false,
                    'message' => 'Failed to create export file.'
                );
            }
            
            // Return download information
            return array(
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'url' => plugins_url('exports/' . $filename, dirname(__FILE__)),
                'count' => count($results),
                'message' => 'Data exported successfully. Download will start automatically.'
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Generate CSV content from data array
     */
    private static function generate_csv_content($data) {
        $csv_content = '';
        
        // Add headers
        $headers = array(
            'Link ID',
            'Course ID', 
            'Product ID',
            'Type',
            'Course Title',
            'Product Title',
            'Course Status',
            'Product Status',
            'Export Date'
        );
        
        $csv_content .= '"' . implode('","', $headers) . '"' . "\n";
        
        // Add data rows
        foreach ($data as $row) {
            $csv_row = array(
                $row['link_id'],
                $row['course_id'],
                $row['product_id'],
                $row['type'],
                str_replace('"', '""', $row['course_title']), // Escape quotes
                str_replace('"', '""', $row['product_title']), // Escape quotes
                $row['course_status'],
                $row['product_status'],
                date('Y-m-d H:i:s')
            );
            
            $csv_content .= '"' . implode('","', $csv_row) . '"' . "\n";
        }
        
        return $csv_content;
    }
    
    /**
     * Get count of records that will be deleted
     */
    public static function get_data_count() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'linking_table';
        
        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
            return 0;
        }
        
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
        return intval($count);
    }
}