<?php
/**
 * Free Course Handler for Custom Linking Plugin
 * 
 * This file handles free course functionality as per Task 3 in Tasks.md:
 * - If a free course is linked with a product, follow normal plugin process
 * - If a free course is not linked to a product, allow direct access
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

/**
 * Initialize free course functionality
 * This function sets up hooks and filters for free course handling
 */
function custom_linking_init_free_course() {
    // Ensure we're not already initialized
    if (defined('CUSTOM_LINKING_FREE_COURSE_INITIALIZED')) {
        custom_linking_debug_log('Free course functionality already initialized');
        return;
    }
    
    custom_linking_debug_log('Initializing free course functionality');
    
    // Mark as initialized
    define('CUSTOM_LINKING_FREE_COURSE_INITIALIZED', true);
    
    // Enqueue free course specific scripts with HIGHER PRIORITY (loads first)
    add_action('wp_enqueue_scripts', 'custom_linking_enqueue_free_course_scripts', 5);    
    // AJAX handlers for free course processing
    add_action('wp_ajax_custom_linking_process_free_course', 'custom_linking_process_free_course');
    add_action('wp_ajax_nopriv_custom_linking_process_free_course', 'custom_linking_process_free_course');
    
    // Hook to check if course is free before processing
    add_filter('custom_linking_before_course_process', 'custom_linking_check_if_course_free', 10, 2);
    
    // Hook to completely bypass plugin processing for users who already have course access
    add_filter('custom_linking_before_course_process', 'custom_linking_bypass_for_enrolled_users', 5, 2);
}

/**
 * Enqueue free course specific JavaScript
 */
function custom_linking_enqueue_free_course_scripts() {
    // Only load on course pages or relevant LMS pages
    if (is_singular('stm-courses') || (function_exists('is_stm_lms_page') && is_stm_lms_page())) {
        
        // CRITICAL: Check if user is already enrolled - if so, DON'T load our JavaScript at all
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();
            $course_id = get_the_ID();
            
            // Use our existing enrollment check function
            if (custom_linking_user_has_course_access($user_id, $course_id)) {
                // User is enrolled - do NOT load our JavaScript to avoid interference
                custom_linking_debug_log("User {$user_id} is enrolled in course {$course_id} - skipping FreeCourse.js to prevent interference");
                return;
            }
            
            // Additional check: Look for "Start Course" button indicators in the page
            // This catches cases where enrollment status might not be immediately reflected
            add_action('wp_footer', function() use ($user_id, $course_id) {
                ?>
                <script type="text/javascript">
                jQuery(document).ready(function($) {
                    // Check if Start Course button exists
                    var hasStartButton = false;
                    
                    // Check for various start course button indicators
                    var startIndicators = [
                        'a[href*="/lesson/"]',
                        'a[href*="/curriculum/"]', 
                        '.start-course',
                        '.continue-course',
                        '.resume-course',
                        '.take-course-btn',
                        'a:contains("Start Course")',
                        'a:contains("Continue Course")',
                        'a:contains("Resume Course")',
                        'a:contains("Take Course")',
                        '.stm-course-start-btn',
                        '.course-start-button'
                    ];
                    
                    startIndicators.forEach(function(selector) {
                        if ($(selector).length > 0) {
                            hasStartButton = true;
                        }
                    });
                    
                    // Also check by button text content
                    $('a, button').each(function() {
                        var text = $(this).text().toLowerCase().trim();
                        if (text.includes('start course') || text.includes('continue course') || 
                            text.includes('resume course') || text.includes('take course') ||
                            text.includes('go to course')) {
                            hasStartButton = true;
                            return false;
                        }
                    });
                    
                    if (hasStartButton) {
                        console.log('[FreeCourse Debug] Start Course button detected - user is enrolled, disabling all course processing JS');
                        
                        // Disable all our JavaScript handlers
                        if (typeof window.customFreeCourseProcessCourse === 'function') {
                            window.customFreeCourseProcessCourse = function() {
                                console.log('[FreeCourse Debug] Course processing disabled - user is enrolled');
                                return false;
                            };
                        }
                        
                        // Remove any existing event handlers
                        $('.stm_lms_buy_button, .get_course_button, .course-buy-button, [data-course-buy]').off('click.freeCourse');
                        
                        // Mark as enrolled user to prevent main plugin interference too
                        $('body').addClass('custom-linking-user-enrolled');
                    }
                });
                </script>
                <?php
            }, 999); // Very late priority to ensure DOM is ready
        }
        
        // Enqueue early enrollment detection script (highest priority)
        wp_enqueue_script(
            'custom-enrollment-detector',
            plugin_dir_url(__FILE__) . 'js/enrollment-detector.js',
            array(), // No dependencies - load as early as possible
            CUSTOM_LINKING_PLUGIN_VERSION,
            false // Load in head for early execution
        );
        
        wp_enqueue_script(
            'custom-free-course-js',
            plugin_dir_url(__FILE__) . 'js/FreeCourse.js',
            array('jquery', 'custom-linking-frontend', 'custom-enrollment-detector'), // Add dependency on enrollment detector
            CUSTOM_LINKING_PLUGIN_VERSION,
            true
        );
        
        // Localize script data
        wp_localize_script(
            'custom-free-course-js',
            'customFreeCourseData',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('custom_linking_free_course_nonce'),
                'console_enabled' => (bool) get_option('custom_linking_enable_console', false),
                'debug' => defined('WP_DEBUG') && WP_DEBUG
            )
        );
    }
}

/**
 * Check if a course is free
 * 
 * @param int $course_id The course ID to check
 * @return bool True if course is free, false otherwise
 */
function custom_linking_is_course_free($course_id) {
    if (!$course_id) {
        return false;
    }
      // Get course price from MasterStudy LMS
    $course_price = get_post_meta($course_id, 'price', true);
    $course_sale_price = get_post_meta($course_id, 'sale_price', true);
    
    // Course is free if:
    // 1. Price is empty, 0, or "0" 
    // 2. OR sale price exists and is 0 or "0" (overrides regular price)
    $price_is_free = empty($course_price) || $course_price == '0' || $course_price === 0;
    $sale_price_is_free = ($course_sale_price !== '' && ($course_sale_price == '0' || $course_sale_price === 0));
    
    $is_free = $price_is_free || $sale_price_is_free;
    
    custom_linking_debug_log("Course {$course_id} price check - Price: '{$course_price}', Sale Price: '{$course_sale_price}', Is Free: " . ($is_free ? 'YES' : 'NO'));
    
    return $is_free;
}

/**
 * Check if course is free and handle accordingly
 * This filter is called before normal course processing
 * 
 * @param array $response Current response
 * @param int $course_id Course ID being processed
 * @return array Modified response if course is free
 */
function custom_linking_check_if_course_free($response, $course_id) {
    // Only process if response is empty (no other module has handled it)
    if (!empty($response)) {
        return $response;
    }
    
    if (!custom_linking_is_course_free($course_id)) {
        // Not a free course, continue with normal processing
        return $response;
    }
    
    custom_linking_debug_log("Processing free course: {$course_id}");
    
    // Check if free course is linked to a product
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    $product_id = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT product_id FROM $table_name WHERE course_id = %d LIMIT 1",
            $course_id
        )
    );    if ($product_id) {
        custom_linking_debug_log("Free course {$course_id} is linked to product {$product_id} - checking user access first");
        
        // IMPORTANT: For linked courses, first check if user already has access
        // If they do, redirect directly to course (don't add to cart again)
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();
            if (custom_linking_user_has_course_access($user_id, $course_id)) {
                custom_linking_debug_log("User {$user_id} already has access to linked free course {$course_id} - redirecting to course");
                
                return array(
                    'success' => true,
                    'message' => "You already have access to this course",
                    'course_id' => $course_id,
                    'product_id' => $product_id,
                    'redirect_to' => 'course',
                    'course_url' => get_permalink($course_id),
                    'is_free' => true,
                    'already_enrolled' => true,
                    'was_linked' => true
                );
            }
        }
        
        // User doesn't have access yet, so add product to cart and follow normal WooCommerce flow
        custom_linking_debug_log("User doesn't have access to linked free course {$course_id} - adding product {$product_id} to cart");
        
        $product = wc_get_product($product_id);
        if ($product) {
            custom_linking_debug_log("Adding linked product {$product_id} to cart for free course {$course_id}");
            
            if (function_exists('WC') && WC()->cart) {
                $cart_item_key = WC()->cart->add_to_cart($product_id);
                
                if ($cart_item_key) {
                    $cart_url = function_exists('wc_get_cart_url') ? wc_get_cart_url() : '';
                    return array(
                        'success' => true,
                        'message' => "Free course linked to product - added to cart",
                        'course_id' => $course_id,
                        'product_id' => $product_id,
                        'cart_added' => true,
                        'cart_url' => $cart_url,
                        'redirect_to' => 'cart',
                        'is_free' => true
                    );
                } else {
                    custom_linking_debug_log("Failed to add product {$product_id} to cart for free course {$course_id}");
                    return array(
                        'success' => false,
                        'message' => "Failed to add product to cart",
                        'course_id' => $course_id,
                        'product_id' => $product_id,
                        'is_free' => true,
                        'error' => 'cart_add_failed'
                    );
                }
            } else {
                custom_linking_debug_log("WooCommerce cart not available for free course {$course_id}");
                return array(
                    'success' => false,
                    'message' => "WooCommerce cart not available",
                    'course_id' => $course_id,
                    'product_id' => $product_id,
                    'is_free' => true,
                    'error' => 'wc_cart_unavailable'
                );
            }
        } else {
            custom_linking_debug_log("Linked product {$product_id} not found for free course {$course_id}");
            return array(
                'success' => false,
                'message' => "Linked product not found",
                'course_id' => $course_id,
                'product_id' => $product_id,
                'is_free' => true,
                'error' => 'product_not_found'
            );
        }
    } else {
        custom_linking_debug_log("Free course {$course_id} is NOT linked to any product - allowing direct access");
        
        // Free course is not linked to a product, allow direct access
        return custom_linking_handle_direct_free_course_access($course_id);
    }
}

/**
 * Handle direct access to free course (not linked to any product)
 * Enhanced with better error handling and logging
 * 
 * @param int $course_id The course ID
 * @return array Response array
 */
function custom_linking_handle_direct_free_course_access($course_id) {
    custom_linking_debug_log("Starting direct free course access for course {$course_id}");
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        custom_linking_debug_log("User not logged in for free course {$course_id} - requiring login");
        
        // Redirect to login with return URL
        $login_url = wp_login_url(get_permalink($course_id));
        
        return array(
            'success' => true,
            'message' => "Please log in to access this free course",
            'course_id' => $course_id,
            'product_id' => null,
            'redirect_to' => 'login',
            'login_url' => $login_url,
            'is_free' => true,
            'direct_access' => false
        );
    }
    
    // User is logged in, get user ID
    $user_id = get_current_user_id();
    custom_linking_debug_log("User {$user_id} is logged in, checking access to course {$course_id}");
    
    // Check if user already has access
    if (custom_linking_user_has_course_access($user_id, $course_id)) {
        custom_linking_debug_log("User {$user_id} already has access to free course {$course_id}");
        
        return array(
            'success' => true,
            'message' => "You already have access to this free course",
            'course_id' => $course_id,
            'product_id' => null,
            'redirect_to' => 'course',
            'course_url' => get_permalink($course_id),
            'is_free' => true,
            'direct_access' => true,
            'already_enrolled' => true
        );
    }
    
    // User doesn't have access, attempt to grant it
    custom_linking_debug_log("User {$user_id} does not have access to course {$course_id}, attempting enrollment");
    
    if (custom_linking_grant_free_course_access($user_id, $course_id)) {
        custom_linking_debug_log("Successfully granted access to free course {$course_id} for user {$user_id}");
        
        // Double-check that access was actually granted
        if (custom_linking_user_has_course_access($user_id, $course_id)) {
            custom_linking_debug_log("Confirmed: User {$user_id} now has access to course {$course_id}");
            
            return array(
                'success' => true,
                'message' => "Access granted to free course",
                'course_id' => $course_id,
                'product_id' => null,
                'redirect_to' => 'course',
                'course_url' => get_permalink($course_id),
                'is_free' => true,
                'direct_access' => true,
                'newly_enrolled' => true
            );
        } else {
            custom_linking_debug_log("WARNING: Enrollment function returned success but user still doesn't have access");
            // Continue with success response anyway, the enrollment may take effect later
            return array(
                'success' => true,
                'message' => "Enrollment completed - please refresh the page to access the course",
                'course_id' => $course_id,
                'product_id' => null,
                'redirect_to' => 'course',
                'course_url' => get_permalink($course_id),
                'is_free' => true,
                'direct_access' => true,
                'newly_enrolled' => true,
                'warning' => 'enrollment_delayed'
            );
        }    } else {
        custom_linking_debug_log("Failed to grant access to free course {$course_id} for user {$user_id}");
        
        // FALLBACK: If enrollment fails, still allow access but warn the user
        // This prevents the "An error occurred while processing" message
        custom_linking_debug_log("Using fallback approach: allowing access despite enrollment failure");
        
        return array(
            'success' => true,
            'message' => "Access granted to free course (direct access)",
            'course_id' => $course_id,
            'product_id' => null,
            'redirect_to' => 'course',
            'course_url' => get_permalink($course_id),
            'is_free' => true,
            'direct_access' => true,
            'enrollment_warning' => true,
            'warning_message' => 'Enrollment may not have been recorded properly, but you can access this free course.'
        );
    }
}

/**
 * Check if user has access to a course
 * Enhanced with multiple access checking strategies and comprehensive logging
 * 
 * @param int $user_id User ID
 * @param int $course_id Course ID
 * @return bool True if user has access, false otherwise
 */
function custom_linking_user_has_course_access($user_id, $course_id) {
    custom_linking_debug_log("Checking access for user {$user_id} to course {$course_id}");
    
    // Strategy 1: Check MasterStudy LMS user course access function
    if (function_exists('stm_lms_has_course_access')) {
        $has_access = stm_lms_has_course_access($course_id, $user_id);
        custom_linking_debug_log("MasterStudy LMS access check result: " . ($has_access ? 'HAS ACCESS' : 'NO ACCESS'));
        if ($has_access) {
            return true;
        }
    }
    
    // Strategy 2: Check if user purchased the course through WooCommerce
    if (function_exists('wc_customer_bought_product') && function_exists('get_current_user_id')) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'linking_table';
        
        // Get linked product for this course
        $product_id = $wpdb->get_var($wpdb->prepare(
            "SELECT product_id FROM {$table_name} WHERE course_id = %d",
            $course_id
        ));
        
        if ($product_id && wc_customer_bought_product('', $user_id, $product_id)) {
            custom_linking_debug_log("User has access via WooCommerce purchase of product {$product_id}");
            return true;
        }
    }
    
    // Strategy 3: Check user meta 'stm_lms_course_passed'
    $user_courses = get_user_meta($user_id, 'stm_lms_course_passed', true);
    if (is_array($user_courses) && in_array($course_id, $user_courses)) {
        custom_linking_debug_log("User has access via 'stm_lms_course_passed' meta");
        return true;
    }
    
    // Strategy 4: Check user meta 'stm_lms_user_courses'
    $user_courses_alt = get_user_meta($user_id, 'stm_lms_user_courses', true);
    if (is_array($user_courses_alt) && in_array($course_id, $user_courses_alt)) {
        custom_linking_debug_log("User has access via 'stm_lms_user_courses' meta");
        return true;
    }
    
    // Strategy 5: Check course-specific enrollment meta
    $course_enrolled = get_user_meta($user_id, "stm_lms_course_{$course_id}_enrolled", true);
    if (!empty($course_enrolled)) {
        custom_linking_debug_log("User has access via course-specific enrollment meta");
        return true;
    }
    
    // Strategy 6: Check user capabilities
    if (function_exists('user_can') && user_can($user_id, "access_course_{$course_id}")) {
        custom_linking_debug_log("User has access via capability");
        return true;
    }
    
    // Strategy 7: Check database directly
    global $wpdb;
    if ($wpdb) {
        $lms_table = $wpdb->prefix . 'stm_lms_user_courses';
        
        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$lms_table}'") == $lms_table;
        
        if ($table_exists) {
            $access = $wpdb->get_var($wpdb->prepare(
                "SELECT user_id FROM {$lms_table} WHERE user_id = %d AND course_id = %d",
                $user_id, $course_id
            ));
            
            if ($access) {
                custom_linking_debug_log("User has access via direct database check");
                return true;
            }
        }
    }
    
    // Strategy 8: Check course students meta
    $course_students = get_post_meta($course_id, 'stm_lms_course_students', true);
    if (is_array($course_students) && in_array($user_id, $course_students)) {
        custom_linking_debug_log("User has access via course students meta");
        return true;
    }
    
    custom_linking_debug_log("User {$user_id} does NOT have access to course {$course_id}");
    return false;
}

/**
 * Grant access to free course for user using MasterStudy LMS native logic
 * Enhanced with the actual LMS enrollment patterns and fallback strategies
 * 
 * @param int $user_id User ID
 * @param int $course_id Course ID
 * @return bool True if access granted successfully, false otherwise
 */
function custom_linking_grant_free_course_access($user_id, $course_id) {
    custom_linking_debug_log("Attempting to grant access to free course {$course_id} for user {$user_id}");      // Strategy 1: Use MasterStudy LMS enrollment function (the primary method)
    if (function_exists('stm_lms_add_user_course')) {
        custom_linking_debug_log("Using MasterStudy LMS stm_lms_add_user_course function");
        
        try {
            // Call with correct parameters for MasterStudy LMS - expects an array
            $user_course_data = array(
                'user_id' => $user_id,
                'course_id' => $course_id,
                'current_lesson_id' => 0,
                'progress_percent' => 0,
                'status' => 'enrolled',
                'start_time' => time()
            );
              $result = stm_lms_add_user_course($user_course_data);
            
            custom_linking_debug_log("MasterStudy LMS enrollment result for user {$user_id}, course {$course_id}: SUCCESS");
            $enrollment_successful = true;
            
            // Also update meta data for complete compatibility
            $user_courses = get_user_meta($user_id, 'stm_lms_course_passed', true);
            if (!is_array($user_courses)) {
                $user_courses = array();
            }
            if (!in_array($course_id, $user_courses)) {
                $user_courses[] = $course_id;
                update_user_meta($user_id, 'stm_lms_course_passed', $user_courses);
                custom_linking_debug_log("Updated user meta stm_lms_course_passed");
            }
            
            // Update course students meta
            $course_students = get_post_meta($course_id, 'stm_lms_course_students', true);
            if (!is_array($course_students)) {
                $course_students = array();
            }
            if (!in_array($user_id, $course_students)) {
                $course_students[] = $user_id;
                update_post_meta($course_id, 'stm_lms_course_students', $course_students);
                custom_linking_debug_log("Updated course students meta");
            }
        } catch (Exception $e) {
            custom_linking_debug_log("MasterStudy LMS stm_lms_add_user_course failed: " . $e->getMessage());
        }
    } else {
        custom_linking_debug_log("MasterStudy LMS stm_lms_add_user_course function not available");
    }
      // Strategy 2: Use STM_LMS_Course class directly (MasterStudy's preferred method)
    if (class_exists('STM_LMS_Course')) {
        custom_linking_debug_log("Using STM_LMS_Course class for enrollment");
        
        try {
            // This mirrors how MasterStudy handles free course enrollment
            // Parameters for add_user_course: course_id, user_id, current_lesson_id, progress_percent, ...
            $result = STM_LMS_Course::add_user_course(
                $course_id,     // course_id
                $user_id,       // user_id
                0,              // current_lesson_id
                0,              // progress_percent
                false,          // is_translate
                '',             // enterprise
                0,              // bundle_id
                '',             // for_points
                ''              // instructor_id
            );
            
            if ($result !== false) {
                custom_linking_debug_log("STM_LMS_Course enrollment successful");
                
                // Also add student count like MasterStudy does
                if (method_exists('STM_LMS_Course', 'add_student')) {
                    STM_LMS_Course::add_student($course_id);
                }
                
                return true;
            }
        } catch (Exception $e) {
            custom_linking_debug_log("STM_LMS_Course enrollment failed: " . $e->getMessage());
        }
    }
    
    // Strategy 3: Direct database insertion using MasterStudy's table structure
    global $wpdb;
    $lms_table = $wpdb->prefix . 'stm_lms_user_courses';
    
    // Check if the table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$lms_table}'") == $lms_table;
    
    if ($table_exists) {
        custom_linking_debug_log("Using direct database insertion with MasterStudy table structure");
        
        // Check if already enrolled
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM {$lms_table} WHERE user_id = %d AND course_id = %d",
            $user_id, $course_id
        ));
        
        if (!$existing) {
            $result = $wpdb->insert(
                $lms_table,
                array(
                    'user_id' => $user_id,
                    'course_id' => $course_id,
                    'status' => 'enrolled',
                    'start_time' => time(),
                    'progress_percent' => 0,
                    'current_lesson_id' => 0,
                    'price' => 0
                ),
                array('%d', '%d', '%s', '%d', '%d', '%d', '%d')
            );
            
            if ($result) {
                custom_linking_debug_log("Direct database enrollment successful");
                
                // Update course student count
                $current_students = get_post_meta($course_id, 'stm_lms_course_students', true);
                if (!is_array($current_students)) {
                    $current_students = array();
                }
                if (!in_array($user_id, $current_students)) {
                    $current_students[] = $user_id;
                    update_post_meta($course_id, 'stm_lms_course_students', $current_students);
                }
                
                return true;
            } else {
                custom_linking_debug_log("Direct database enrollment failed: " . $wpdb->last_error);
            }
        } else {
            custom_linking_debug_log("User already enrolled in database");
            return true;
        }
    }
    
    // Strategy 4: Use MasterStudy's user meta patterns (fallback)
    custom_linking_debug_log("Using MasterStudy user meta enrollment patterns");
    
    $enrollment_success = false;
    
    // Meta key 1: stm_lms_course_passed (MasterStudy's primary meta)
    $user_courses = get_user_meta($user_id, 'stm_lms_course_passed', true);
    if (!is_array($user_courses)) {
        $user_courses = array();
    }
    
    if (!in_array($course_id, $user_courses)) {
        $user_courses[] = $course_id;
        $result1 = update_user_meta($user_id, 'stm_lms_course_passed', $user_courses);
        custom_linking_debug_log("User meta 'stm_lms_course_passed' update result: " . ($result1 ? 'SUCCESS' : 'FAILED'));
        if ($result1) {
            $enrollment_success = true;
        }
    } else {
        custom_linking_debug_log("User {$user_id} already in 'stm_lms_course_passed' for course {$course_id}");
        $enrollment_success = true;
    }
    
    // Meta key 2: stm_lms_user_courses (alternative MasterStudy meta)
    $user_courses_alt = get_user_meta($user_id, 'stm_lms_user_courses', true);
    if (!is_array($user_courses_alt)) {
        $user_courses_alt = array();
    }
    
    if (!in_array($course_id, $user_courses_alt)) {
        $user_courses_alt[] = $course_id;
        $result2 = update_user_meta($user_id, 'stm_lms_user_courses', $user_courses_alt);
        custom_linking_debug_log("User meta 'stm_lms_user_courses' update result: " . ($result2 ? 'SUCCESS' : 'FAILED'));
        if ($result2) {
            $enrollment_success = true;
        }
    }
    
    // Meta key 3: Course-specific enrollment (MasterStudy pattern)
    $result3 = update_user_meta($user_id, "stm_lms_course_{$course_id}_enrolled", time());
    custom_linking_debug_log("Course-specific enrollment meta update result: " . ($result3 ? 'SUCCESS' : 'FAILED'));
    if ($result3) {
        $enrollment_success = true;
    }
    
    // Meta key 4: Course access capability (WordPress capability system)
    if (function_exists('get_userdata')) {
        $user = get_userdata($user_id);
        if ($user && method_exists($user, 'add_cap')) {
            $user->add_cap("access_course_{$course_id}");
            custom_linking_debug_log("Added capability access_course_{$course_id} to user {$user_id}");
            $enrollment_success = true;
        }
    }
      // Add course to course's student list (MasterStudy pattern)
    $course_students = get_post_meta($course_id, 'stm_lms_course_students', true);
    if (!is_array($course_students)) {
        $course_students = array();
    }
    if (!in_array($user_id, $course_students)) {
        $course_students[] = $user_id;
        update_post_meta($course_id, 'stm_lms_course_students', $course_students);
        custom_linking_debug_log("Added user {$user_id} to course {$course_id} students list");
    }
    
    // CRITICAL: Trigger MasterStudy LMS hooks and actions after enrollment
    if ($enrollment_success) {
        custom_linking_debug_log("Triggering MasterStudy LMS enrollment hooks for user {$user_id}, course {$course_id}");
        
        // Fire WordPress action hooks that MasterStudy LMS uses for course enrollment
        do_action('stm_lms_course_enrolled', $user_id, $course_id);
        do_action('stm_lms_user_course_started', $course_id, $user_id);
        do_action('stm_lms_after_course_enrolled', $user_id, $course_id);
        do_action('stm_lms_progress_updated', $user_id, $course_id, 0, 0);
        
        // Update user meta that MasterStudy might check for course access
        update_user_meta($user_id, 'stm_lms_courses_enrolled_' . $course_id, time());
        update_user_meta($user_id, 'stm_lms_course_start_' . $course_id, time());
        update_user_meta($user_id, 'stm_lms_course_progress_' . $course_id, 0);
        
        // Clear any caches that might prevent access detection
        if (function_exists('wp_cache_delete')) {
            wp_cache_delete("user_courses_{$user_id}", 'stm_lms');
            wp_cache_delete("course_access_{$course_id}_{$user_id}", 'stm_lms');
            wp_cache_delete("user_progress_{$user_id}_{$course_id}", 'stm_lms');
        }
        
        // Refresh any transients
        delete_transient("stm_lms_user_{$user_id}_courses");
        delete_transient("stm_lms_course_{$course_id}_students");
    }
    
    custom_linking_debug_log("Final enrollment result for user {$user_id}, course {$course_id}: " . ($enrollment_success ? 'SUCCESS' : 'FAILED'));
    
    return $enrollment_success;
}

/**
 * Bypass plugin processing for users who already have course access
 * This prevents interference with "Start Course" buttons for enrolled users
 * 
 * @param array $response Current response
 * @param int $course_id Course ID
 * @return array Empty response if user has access (bypasses plugin), original response otherwise
 */
function custom_linking_bypass_for_enrolled_users($response, $course_id) {
    // Only check for logged-in users
    if (!is_user_logged_in()) {
        return $response;
    }
    
    $user_id = get_current_user_id();
    
    // If user already has access to the course, bypass all plugin processing
    if (custom_linking_user_has_course_access($user_id, $course_id)) {
        custom_linking_debug_log("User {$user_id} already has access to course {$course_id} - bypassing all plugin processing");
        
        // Return empty response to let MasterStudy LMS handle the "Start Course" functionality normally
        return array();
    }
    
    return $response;
}

/**
 * AJAX handler for processing free course requests
 * Enhanced with better error handling and debugging
 */
function custom_linking_process_free_course() {
    custom_linking_debug_log('=== FREE COURSE AJAX REQUEST STARTED ===');
    custom_linking_debug_log('POST data: ' . print_r($_POST, true));
    
    // Verify nonce
    $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : '';
    if (empty($nonce) || !wp_verify_nonce($nonce, 'custom_linking_free_course_nonce')) {
        custom_linking_debug_log('Free course security verification failed - invalid or missing nonce');
        wp_send_json_error(array('message' => 'Security check failed'));
        return;
    }
    
    // Get course ID
    $course_id = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    
    if (!$course_id) {
        custom_linking_debug_log('Error: No course ID provided for free course processing');
        wp_send_json_error(array('message' => 'No course ID provided'));
        return;
    }
    
    custom_linking_debug_log("Processing AJAX request for course {$course_id}");
    
    // Check if course is actually free
    if (!custom_linking_is_course_free($course_id)) {
        custom_linking_debug_log("Course {$course_id} is not free - redirecting to normal processing");
        wp_send_json_error(array('message' => 'Course is not free', 'redirect_to_normal' => true));
        return;
    }
    
    // Log user status
    $user_logged_in = is_user_logged_in();
    $user_id = $user_logged_in ? get_current_user_id() : 0;
    custom_linking_debug_log("User logged in: " . ($user_logged_in ? "YES (ID: $user_id)" : "NO"));
    
    try {
        // Process free course
        $response = custom_linking_check_if_course_free(array(), $course_id);
        
        custom_linking_debug_log('Free course processing result: ' . print_r($response, true));
        
        if ($response && isset($response['success'])) {
            if ($response['success']) {
                custom_linking_debug_log("Free course processing successful for course {$course_id}");
                wp_send_json_success($response);
            } else {
                custom_linking_debug_log("Free course processing failed for course {$course_id}: " . (isset($response['message']) ? $response['message'] : 'Unknown error'));
                wp_send_json_error($response);
            }
        } else {
            custom_linking_debug_log("Free course processing returned invalid response for course {$course_id}");
            wp_send_json_error(array(
                'message' => 'Invalid response from free course processor',
                'error' => 'invalid_response'
            ));
        }
    } catch (Exception $e) {
        custom_linking_debug_log("Exception in free course processing for course {$course_id}: " . $e->getMessage());
        wp_send_json_error(array(
            'message' => 'An error occurred while processing the free course',
            'error' => 'exception',
            'details' => $e->getMessage()
        ));
    }
}

// Initialize free course functionality
custom_linking_init_free_course();
