"use strict";

/**
 * Custom Linking Plugin - Frontend JS
 *
 * This JavaScript file modifies the MasterStudy LMS "Get Course" button functionality
 * It keeps the original functionality intact while adding our custom override
 * for redirecting to linked WooCommerce products
 */
(function ($) {
  /**
   * This block checks if we have the required data to make AJAX calls
   * If not, we'll log an error and try to use reasonable defaults
   */
  var debug = false; // Disable console logging by default
  // Allow WordPress to enable this via localized script variable
  if ( typeof customLinkingData !== 'undefined' && typeof customLinkingData.console_enabled !== 'undefined' ) {
    debug = !!customLinkingData.console_enabled;
  }
  // If debugging is off, silence console output from this script
  if ( !debug ) {
    ['log','error','warn','info','debug'].forEach(function(m){
      if (typeof console[m] === 'function') {
        console[m] = function(){};
      }
    });
  }
  
  /**
   * Improved logging function that can be enabled/disabled
   * This helps keep console clean in production while still allowing debugging
   */
  function debugLog(message, data) {
    if (!debug) return;
    // Previously logged to console; now silent to keep console clean.
  }
  
  /**
   * Get AJAX URL and nonce from localized data or use defaults
   * This helps the plugin work even if WordPress doesn't properly localize the script
   */
  var ajaxUrl = '/wp-admin/admin-ajax.php';
  var ajaxNonce = '';
  
  if (typeof customLinkingData !== 'undefined') {
    ajaxUrl = customLinkingData.ajax_url || ajaxUrl;
    ajaxNonce = customLinkingData.nonce || '';
    if(debug) debugLog('Using AJAX URL: ' + ajaxUrl + ' (Nonce ' + (ajaxNonce ? 'present' : 'missing') + ')');
  } else {
    if(debug) console.error('Custom Linking Plugin: customLinkingData not found - using default AJAX URL');
  }
  
  if(debug) debugLog('Initializing and looking for course buttons...');
  
  /**
   * Enhanced course data extraction
   * This function tries multiple ways to get the course ID from the page
   */
  function getCourseId() {
    var courseId = null;
    
    // Method 1: From data attribute on parent element
    var courseElement = $('[data-purchased-course]');
    if (courseElement.length) {
      courseId = courseElement.attr('data-purchased-course');
      if(debug) debugLog('Found course ID from data attribute: ' + courseId);
      return courseId;
    }
    
    // Method 2: From course ID in URL /courses/ID/ format
    var urlParts = window.location.pathname.split('/');
    for (var i = 0; i < urlParts.length; i++) {
      if ((urlParts[i] === 'courses' || urlParts[i] === 'course') && i+1 < urlParts.length) {
        courseId = urlParts[i+1];
        if(debug) debugLog('Found course ID from URL path: ' + courseId);
        return courseId;
      }
    }
    
    // Method 3: From page body class stm-courses-{ID}
    var bodyClasses = $('body').attr('class');
    if (bodyClasses) {
      var courseMatch = bodyClasses.match(/stm-courses-(\d+)/);
      if (courseMatch && courseMatch[1]) {
        courseId = courseMatch[1];
        if(debug) debugLog('Found course ID from body class: ' + courseId);
        return courseId;
      }
    }
    
    // Method 4: From post ID meta
    var postId = $('meta[property="article:id"]').attr('content');
    if (postId) {
      if(debug) debugLog('Found potential course ID from post metadata: ' + postId);
      return postId;
    }
    
    // Last resort: try to find any numeric ID in the URL
    var idMatch = window.location.pathname.match(/\/(\d+)\/?$/);
    if (idMatch && idMatch[1]) {
      if(debug) debugLog('Found numeric ID from URL: ' + idMatch[1]);
      return idMatch[1];
    }
    
    if(debug) debugLog('Could not determine course ID');
    return 'unknown';
  }
  
  /**
   * Check if user is enrolled in the current course
   * This prevents interference with Start Course buttons
   */
  function isUserEnrolledInCurrentCourse() {
    // Check global flags first
    if (window.customFreeCourseUserEnrolled === true || $('body').hasClass('custom-linking-user-enrolled')) {
      return true;
    }
    
    // Check for Start Course buttons
    var startButtons = $(
      'a[href*="/lesson/"], a[href*="/curriculum/"], ' +
      '.start-course, .continue-course, .resume-course, ' +
      '.take-course-btn, .stm-course-start-btn'
    );
    
    var hasStartButton = startButtons.length > 0;
    
    // Also check by text content
    if (!hasStartButton) {
      $('a, button').each(function() {
        var text = $(this).text().toLowerCase().trim();
        if (text.includes('start course') || text.includes('continue course') || 
            text.includes('resume course') || text.includes('take course')) {
          hasStartButton = true;
          return false;
        }
      });
    }
    
    if (hasStartButton) {
      // Mark globally
      $('body').addClass('custom-linking-user-enrolled');
      window.customFreeCourseUserEnrolled = true;
      debugLog('Start Course button detected - user is enrolled');
      return true;
    }
    
    return false;
  }

  // Our handler for the GET COURSE button
  function interceptBuyButtonClick(e) {
    if(debug) debugLog('Button clicked - intercepting for processing');
    
    // CRITICAL: Check if user is enrolled before any processing
    if (window.customFreeCourseUserEnrolled === true || $('body').hasClass('custom-linking-user-enrolled')) {
      debugLog('User is enrolled - allowing default LMS behavior for Start Course button');
      return true; // Allow default behavior
    }
    
    // Check for Start Course button indicators
    var $button = $(this);
    var buttonText = $button.text().toLowerCase().trim();
    var href = $button.attr('href') || '';
    
    // If this is clearly a Start Course button, don't interfere
    if (buttonText.includes('start course') || buttonText.includes('continue course') || 
        buttonText.includes('resume course') || buttonText.includes('take course') ||
        buttonText.includes('go to course') || href.includes('/lesson/') || href.includes('/curriculum/')) {
      debugLog('Start/Continue course button detected - allowing default LMS behavior');
      return true; // Allow default behavior
    }
    
    // Prevent the default action immediately to avoid conflicts
    e.preventDefault();
    e.stopPropagation();
    
    // Get course ID
    var courseId = getCourseId();
    if (!courseId) {
      if(debug) debugLog('No course ID found - allowing default behavior');
      return false;
    }
    
    // INTEGRATION: Check if this is a free course first
    if (typeof window.customFreeCourseCheckIfFree === 'function' && window.customFreeCourseCheckIfFree()) {
      debugLog('Course appears to be free - delegating to free course handler');
      
      // Let free course handler process this
      if (typeof window.customFreeCourseProcessCourse === 'function') {
        var handled = window.customFreeCourseProcessCourse(courseId);
        if (handled) {
          debugLog('Free course handler processed the request');
          return false; // Stop here
        }
      }
      
      debugLog('Free course handler could not process - continuing with normal flow');
    }
    
    if(debug) debugLog('Intercepted click for course: ' + courseId);

    /**
     * Set a flag to indicate we're processing the click
     * This prevents duplicate calls if user clicks multiple times
     */
    if ($(this).data('processing')) {
      if(debug) debugLog('Already processing this button click - ignoring duplicate');
      return false;
    }
    
    $(this).data('processing', true);
    var $clickedButton = $(this);
    
    /**
     * Show visual feedback to user while processing
     * This lets them know something is happening
     */
    var originalButtonText = $clickedButton.text();
    $clickedButton.text('Processing...').addClass('processing');
    
    // Make AJAX call to query course-product link
    $.ajax({
      url: ajaxUrl,
      type: 'POST',
      async: true,
      timeout: 15000, // 15 second timeout to prevent hanging requests
      cache: false,   // Prevent caching - important for reliable responses
      data: {
        action: 'custom_linking_course_request',
        course_id: courseId,
        security: ajaxNonce // Use 'security' as the param name which is standard for WP nonces
      },
      success: function(response) {
        if(debug) debugLog('AJAX response received:', response);
        
        /**
         * Reset the button state first
         * This provides good UX if there's an error or delay
         */
        $clickedButton.text(originalButtonText).removeClass('processing').data('processing', false);

        /**
         * Process the successful response
         * First check that we have a valid response structure
         * Then handle different redirect types accordingly
         */
        if (response && response.success === true && response.data) {
          if (response.data.redirect_to === 'cart' && response.data.cart_url) {
            // Redirect to WooCommerce cart
            if(debug) debugLog('Redirecting to cart: ' + response.data.cart_url);
            // Use setTimeout to ensure the AJAX success completes first
            setTimeout(function() {
              window.location.href = response.data.cart_url;
            }, 100);
            return;
          } else if (response.data.redirect_to === 'product' && response.data.product_url) {
            // Redirect to product page
            if(debug) debugLog('Redirecting to product page: ' + response.data.product_url);
            // Use setTimeout to ensure the AJAX success completes first
            setTimeout(function() {
              window.location.href = response.data.product_url;
            }, 100);
            return;
          } else {
            if(debug) debugLog('No redirect needed or no linked product found');
          }
        } else {
          if(debug) console.warn('Custom Linking Plugin: Invalid or unexpected response format', response);
        }
      },
      error: function(xhr, status, error) {
        /**
         * Reset the button state in case of error
         * This allows the user to try again if needed
         */
        $clickedButton.text(originalButtonText).removeClass('processing').data('processing', false);
        
        /**
         * Enhanced error logging to provide more details about AJAX failures
         * This helps identify why the request is failing
         */
        if(debug) console.error('Custom Linking Plugin: AJAX error details:', {
          error: error,
          status: status,
          responseText: xhr.responseText,
          statusText: xhr.statusText,
          readyState: xhr.readyState,
          url: ajaxUrl
        });
        
        /**
         * Special handling for readyState 0 (request not initialized/aborted)
         * This typically happens when the page is being navigated away during the request
         * In this case, we can just ignore the error as it's expected behavior
         */
        if (xhr.readyState === 0) {
          if(debug) debugLog('AJAX request aborted - likely due to page navigation, this is expected');
          return;
        }
        
        // For regular errors, try to parse the response if it exists
        if (xhr.responseText) {
          try {
            var jsonResponse = JSON.parse(xhr.responseText);
            if(debug) console.error('Custom Linking Plugin: Parsed error response:', jsonResponse);
            
            // Check if we have a redirect URL in the JSON response
            if (jsonResponse && jsonResponse.data) {
              if (jsonResponse.data.product_url) {
                if(debug) debugLog('Found product URL in error response JSON, redirecting to: ' + jsonResponse.data.product_url);
                setTimeout(function() {
                  window.location.href = jsonResponse.data.product_url;
                }, 100);
                return;
              } else if (jsonResponse.data.cart_url) {
                if(debug) debugLog('Found cart URL in error response JSON, redirecting to: ' + jsonResponse.data.cart_url);
                setTimeout(function() {
                  window.location.href = jsonResponse.data.cart_url;
                }, 100);
                return;
              }
            }
            
          } catch (e) {
            // Not JSON, just log the raw text
            if(debug) console.error('Custom Linking Plugin: Raw error response:', xhr.responseText);
            
            /**
             * Try to extract a URL from the response text
             * Look for patterns like redirecting to product page: http://...
             */
            var urlMatch = /redirecting to (product page|cart): ([^\s]+)/.exec(xhr.responseText || '');
            if (urlMatch && urlMatch[2]) {
              var extractedUrl = urlMatch[2];
              if(debug) debugLog('Extracted URL from error response text: ' + extractedUrl);
              setTimeout(function() {
                window.location.href = extractedUrl;
              }, 100);
              return;
            }
          }
        }
        
        /**
         * If we got here, we couldn't handle the error automatically
         * Log a user-friendly message
         */
        if(debug) console.warn('Custom Linking Plugin: Could not process the course request. Please try again.');
      }
    });
    
    return false;
  }
  
  /**
   * Complete replacement of button behavior instead of intercepting
   * This ensures we totally override MasterStudy's default behavior
   */
  function attachClickHandlersToButtons() {
    // Define all possible button selectors to look for
    var buttonSelectors = [
      '.masterstudy-buy-button__link',  // New MasterStudy theme
      '.stm-lms-buy-buttons .btn',      // Common button in buy section
      '#stm_lms_buy_button',            // Main buy button by ID
      '.btn-default[data-buy-course]',  // Data attribute button
      '.btn-default.buy-button',        // Default buy button
      'a.btn[href*="buy-course"]',     // Link with buy-course in URL
      '.stm_lms_mixed_button__purchase', // Mixed button purchase component
      '.lms_purchase_button',           // Generic purchase button
      'a[href*="add-to-cart"]'         // Generic add to cart link
    ];
    
    // Join all selectors into one string with commas
    var combinedSelector = buttonSelectors.join(', ');
    
    /**
     * First, remove all existing click handlers
     * This is crucial for preventing event conflicts with MasterStudy
     */
    jQuery(document).off('click', combinedSelector)
      .find(combinedSelector).each(function() {
        var $btn = jQuery(this);
        // Clone the button to completely remove all event handlers
        var $newBtn = $btn.clone(false);
        // Copy any inline styles that might be important
        $newBtn.attr('style', $btn.attr('style'));
        // Replace old button with new one
        $btn.replaceWith($newBtn);
      });
      
    // Re-query the buttons after replacement
    var $buttons = jQuery(combinedSelector);
    
    // Log how many buttons we found
    if(debug) debugLog('GET COURSE button found: ' + $buttons.length, $buttons);
    
    /**
     * Attach our custom handler with direct action
     * Don't use normal click handler - use direct action
     */
    if ($buttons.length > 0) {
      $buttons.each(function() {
        var $btn = jQuery(this);
        // First, prevent any default behavior by making href='javascript:void(0)'
        if ($btn.attr('href')) {
          // Store original href for debugging
          $btn.attr('data-original-href', $btn.attr('href'));
          $btn.attr('href', 'javascript:void(0)');
        }
        // Remove any other click-related attributes
        $btn.removeAttr('onclick');
        $btn.removeAttr('data-buy-course');
        $btn.removeAttr('data-stm-lms-modal');
        
        // Now attach our handler with a direct action
        $btn.on('click', function(e) {
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          interceptBuyButtonClick.call(this, e);
          return false;
        });
      });
      if(debug) debugLog('Completely replaced ' + $buttons.length + ' buttons with our custom handlers');
    } else {
      // Fallback: try to find any button or link that contains "GET COURSE" text
      jQuery('button, a.btn').each(function() {
        var $btn = jQuery(this);
        var buttonText = $btn.text().toUpperCase().trim();
        if (buttonText.indexOf('GET COURSE') !== -1 || buttonText.indexOf('ENROLL') !== -1) {
          // Replace this button too using same technique
          var $newBtn = $btn.clone(false);
          $newBtn.attr('style', $btn.attr('style'));
          if ($newBtn.attr('href')) {
            $newBtn.attr('data-original-href', $newBtn.attr('href'));
            $newBtn.attr('href', 'javascript:void(0)');
          }
          $newBtn.removeAttr('onclick');
          $newBtn.on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            interceptBuyButtonClick.call(this, e);
            return false;
          });
          $btn.replaceWith($newBtn);
          if(debug) debugLog('Fallback handler replaced button with text: ' + $newBtn.text());
        }
      });
    }
  }
  
  /**
   * Use MutationObserver to detect dynamically added buttons
   * This will catch buttons that are added after page load
   * Using a debounced approach to prevent excessive processing
   */
  function observeDynamicButtonAddition() {
    if (!window.MutationObserver) {
      if(debug) debugLog('MutationObserver not supported in this browser');
      return;
    }
    
    var debounceTimeout = null;
    var observer = new MutationObserver(function(mutations) {
      // Check if any mutations contain nodes that could be our buttons
      var hasRelevantChanges = false;
      mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
          for (var i = 0; i < mutation.addedNodes.length; i++) {
            var node = mutation.addedNodes[i];
            if (node.nodeType === 1) { // Only process Element nodes
              var $node = jQuery(node);
              // Check if this is a button or contains buttons
              if ($node.is('a, button, .btn') || $node.find('a, button, .btn').length) {
                hasRelevantChanges = true;
                break;
              }
            }
          }
        }
      });
      
      // If we detected relevant changes, debounce the reattachment
      if (hasRelevantChanges) {
        if(debug) debugLog('Dynamic content change detected - will refresh button handlers');
        
        // Clear any existing timeout
        if (debounceTimeout) {
          clearTimeout(debounceTimeout);
        }
        
        // Set a new timeout to reattach handlers after mutations settle
        debounceTimeout = setTimeout(function() {
          if(debug) debugLog('Applying button handler updates after DOM changes');
          attachClickHandlersToButtons();
        }, 300); // Wait 300ms after changes stop
      }
    });
    
    // Start observing document body for changes
    observer.observe(document.body, { 
      childList: true, 
      subtree: true 
    });
    if(debug) debugLog('Dynamic button detection enabled with debouncing');
  }
  
  /**
   * Initialize when document is ready
   * Main entry point for the plugin
   */
  $(document).ready(function() {
    if(debug) debugLog('Custom Linking Plugin initialization starting...');
    
    // CRITICAL: Check if guest redirect is active (user is not logged in)
    if (typeof clpGuest !== 'undefined' && clpGuest.logged_in === '0') {
      debugLog('Guest user detected - guest redirect should handle button clicks, exiting course linking');
      return; // Exit early - let guest redirect handle all clicks
    }
    
    // CRITICAL: Check if user is enrolled before doing anything
    if (isUserEnrolledInCurrentCourse()) {
      debugLog('User is enrolled in current course - disabling all course linking functionality');
      return; // Exit early - don't attach any handlers
    }
    
    /**
     * Check if customLinkingData is properly loaded
     * This ensures we have a valid AJAX URL and nonce
     */
    if (typeof customLinkingData === 'undefined') {
      if(debug) console.error('Custom Linking Plugin: Missing customLinkingData object. Plugin may not be properly loaded or registered.');
    } else {
      if(debug) debugLog('Custom Linking Plugin: Initialized with AJAX URL: ' + customLinkingData.ajax_url);
      if(debug) debugLog('Custom Linking Plugin: Nonce is ' + (customLinkingData.nonce ? 'present' : 'missing'));
    }
    
    // ------------------ NEW DELEGATED HANDLER SETUP ------------------
    // Instead of cloning and replacing buttons, register a single delegated
    // click listener on the document. This eliminates race conditions where
    // a user’s first click lands on an element that has just been replaced.
    // Selector collects all variants previously covered by the legacy logic.
    var BUY_BUTTON_SELECTOR = [
      '#stm_lms_buy_button',
      '.get_course_button',
      '.masterstudy-buy-button__link',
      '.stm-single-course-btn',
      '.btn-default[data-buy-course]',
      '.btn-default.buy-button',
      'a.btn[href*="buy-course"]',
      '.stm_lms_mixed_button__purchase',
      '.lms_purchase_button',
      'a[href*="add-to-cart"]'
    ].join(', ');

    // Attach once; namespace prevents collisions with guest script.
    $(document).on('click.customLinking', BUY_BUTTON_SELECTOR, function(e){
      interceptBuyButtonClick.call(this, e);
    });
    if(debug) debugLog('Delegated handler attached for selector: ' + BUY_BUTTON_SELECTOR);
    // ------------------ END NEW SETUP ------------------

    // NOTE: attachClickHandlersToButtons() and observeDynamicButtonAddition()
    // are left in the file for backward compatibility but are no longer invoked.
    
    if(debug) debugLog('Custom Linking Plugin initialization complete!');
    
    /**
     * This block handles subscription button styling
     * Original MasterStudy LMS code - keep intact
     */
    $('.stm_lms_mixed_button.subscription_enabled > .btn').on('click', function () {
      var height_list = $('.stm_lms_mixed_button__list')[0].clientHeight + 40;
      var sticky_buttons = $('.stm-lms-buy-buttons-sticky');
      var sticky_buy_buttons = $('.stm-lms-buy-buttons-mixed');

      if (sticky_buttons.length === 0) {
        sticky_buy_buttons.addClass('stm-lms-buy-buttons-sticky');
        sticky_buy_buttons.css('margin-bottom', height_list + 'px');
        $('.stm-lms-buy-buttons-enterprise').css('margin-bottom', '-165px');
      } else {
        sticky_buy_buttons.css('margin-bottom', '15px');
        $('.stm-lms-buy-buttons-enterprise').css('margin-bottom', '15px');
        sticky_buy_buttons.removeClass('stm-lms-buy-buttons-sticky');
      }

      $('.stm_lms_mixed_button').toggleClass('active');
    });
    
    /**
     * This block handles click outside detection
     * Original MasterStudy LMS code - keep intact
     */
    var $body = $('body');
    var buy_buttons = '.stm-lms-buy-buttons';
    $body.click(function (e) {
      if (!$(buy_buttons).is(e.target) && $(buy_buttons).has(e.target).length === 0 && !$('.stm_lms_course_sticky_panel__button').is(e.target) && $('.stm_lms_course_sticky_panel__button').has(e.target).length === 0) {
        // if div is not target nor its descendant
        $('.stm-lms-buy-buttons-sticky').css('margin-bottom', '15px');
        $(buy_buttons).removeClass('stm-lms-buy-buttons-sticky');
        $('.stm_lms_mixed_button').removeClass('active');
      }
    });
    
    /**
     * CUSTOM OVERRIDE: Get Course button click handlers
     * This is the main override functionality that intercepts clicks on the "Get Course" button
     * We use multiple selectors to ensure we catch all possible button variations
     */
    // Add debug information to see what buttons are available
    if(debug) console.log('Custom Linking Plugin: Looking for course buttons...');
    if(debug) console.log('GET COURSE button found:', $('.get_course_button').length);
    if(debug) console.log('stm_lms_buy_button found:', $('#stm_lms_buy_button').length);
    if(debug) console.log('GET COURSE text buttons found:', $('a:contains("GET COURSE")').length);
    
    /**
     * Enhanced button detection: We'll look for buttons using multiple selectors
     * since themes and plugins may use different class combinations
     * This helps ensure we catch all possible "Get Course" buttons
     */
    if(debug) console.log('Custom Linking Plugin: Looking for course buttons...');
    
    // Count available buttons for debugging
    var getCourseButtons = $('.get_course_button, .btn:contains("GET COURSE"), a:contains("GET COURSE")');
    var stmLmsBuyButtons = $('#stm_lms_buy_button');
    var textButtons = $('.btn-default:contains("GET COURSE")');
    var allPossibleButtons = $('.stm-lms-buy-buttons .btn, .btn-default, .masterstudy-buy-button__link, button.lms_price_btn');
    
    // Log button counts
    if(debug) console.log('GET COURSE button found:', getCourseButtons.length);
    if(debug) console.log('stm_lms_buy_button found:', stmLmsBuyButtons.length);
    if(debug) console.log('GET COURSE text buttons found:', textButtons.length);
    if(debug) console.log('Total possible buy buttons found:', allPossibleButtons.length);
    
    // Log all button details for debugging
    allPossibleButtons.each(function() {
      if(debug) console.log('Found button:', $(this).text(), 'Classes:', $(this).attr('class'), 'ID:', $(this).attr('id'));
    });
    
    // Target by ID - Main buy button
    $body.on('click', '#stm_lms_buy_button', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      if(debug) console.log('Custom Linking Plugin: Buy button clicked by ID!');
      
      // Get course ID from data attribute or from URL
      var courseId = $(this).data('buy-course');
      if (!courseId) {
        // Try to extract from URL if not in data attribute
        var urlParts = window.location.pathname.split('/');
        for (var i = 0; i < urlParts.length; i++) {
          if (urlParts[i] === 'course' && i+1 < urlParts.length) {
            courseId = urlParts[i+1];
            break;
          }
        }
      }
      
      // Log for debugging
      if(debug) console.log('Custom Linking Plugin: Intercepted click on course ID ' + courseId);
      
      // Make AJAX call to query course-product link
      $.ajax({
        url: customLinkingData.ajax_url || '/wp-admin/admin-ajax.php',
        type: 'POST',
        data: {
          action: 'custom_linking_course_request',
          course_id: courseId,
          nonce: customLinkingData.nonce || ''
        },
        success: function(response) {
          if(debug) console.log('Custom Linking Plugin: AJAX response:', response);

          // Handle successful response
          if (response.success && response.data) {
            if (response.data.redirect_to === 'cart' && response.data.cart_url) {
              // Redirect to WooCommerce cart
              if(debug) console.log('Custom Linking Plugin: Redirecting to cart:', response.data.cart_url);
              window.location.href = response.data.cart_url;
            } else if (response.data.redirect_to === 'product' && response.data.product_url) {
              // Redirect to product page
              if(debug) console.log('Custom Linking Plugin: Redirecting to product page:', response.data.product_url);
              window.location.href = response.data.product_url;
            } else {
              if(debug) console.log('Custom Linking Plugin: No redirect needed or no linked product found');
              // If no redirection is needed, we can let the default MasterStudy behavior happen
            }
          } else {
            if(debug) console.log('Custom Linking Plugin: Invalid response format, unable to process');
          }
        },
        error: function(xhr, status, error) {
          /**
           * Enhanced error logging to provide more details about AJAX failures
           * This helps identify why the request is failing
           */
          if(debug) console.error('Custom Linking Plugin: AJAX error details:', {
            error: error,
            status: status,
            responseText: xhr.responseText,
            statusText: xhr.statusText
          });
        }
      });
      
      return false;
    });
    
    // Target by button text - Generic GET COURSE button
    $body.on('click', '.get_course_button, .btn-default:contains("GET COURSE")', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      if(debug) console.log('Custom Linking Plugin: GET COURSE button clicked!');
      
      // Extract course ID from URL
      var courseId = null;
      var urlParts = window.location.pathname.split('/');
      for (var i = 0; i < urlParts.length; i++) {
        if (urlParts[i] === 'course' && i+1 < urlParts.length) {
          courseId = urlParts[i+1];
          break;
        }
      }
      
      // If we couldn't find the course ID in URL, try data attributes
      if (!courseId && $(this).data('course-id')) {
        courseId = $(this).data('course-id');
      }
      
      if(debug) console.log('Custom Linking Plugin: Found course ID: ' + courseId);

      // Make AJAX call to query course-product link
      $.ajax({
        url: customLinkingData.ajax_url || '/wp-admin/admin-ajax.php',
        type: 'POST',
        data: {
          action: 'custom_linking_course_request',
          course_id: courseId,
          nonce: customLinkingData.nonce || ''
        },
        success: function(response) {
          if(debug) console.log('Custom Linking Plugin: AJAX response:', response);

          // Handle successful response
          if (response.success && response.data) {
            if (response.data.redirect_to === 'cart' && response.data.cart_url) {
              // Redirect to WooCommerce cart
              if(debug) console.log('Custom Linking Plugin: Redirecting to cart:', response.data.cart_url);
              window.location.href = response.data.cart_url;
            } else if (response.data.redirect_to === 'product' && response.data.product_url) {
              // Redirect to product page
              if(debug) console.log('Custom Linking Plugin: Redirecting to product page:', response.data.product_url);
              window.location.href = response.data.product_url;
            } else {
              if(debug) console.log('Custom Linking Plugin: No redirect needed or no linked product found');
              // If no redirection is needed, we can let the default MasterStudy behavior happen
            }
          } else {
            if(debug) console.log('Custom Linking Plugin: Invalid response format, unable to process');
          }
        },
        error: function(xhr, status, error) {
          /**
           * Enhanced error logging to provide more details about AJAX failures
           * This helps identify why the request is failing
           */
          if(debug) console.error('Custom Linking Plugin: AJAX error details:', {
            error: error,
            status: status,
            responseText: xhr.responseText,
            statusText: xhr.statusText
          });
        }
      });
      
      return false;
    });
    
    // Most generic catch-all selector for any GET COURSE button
    $body.on('click', '.stm-lms-buy-buttons .btn', function(e) {
      if(debug) console.log('Custom Linking Plugin: Generic button clicked!');
      e.preventDefault();
      
      // Extract course ID from URL
      var courseId = null;
      var urlParts = window.location.pathname.split('/');
      for (var i = 0; i < urlParts.length; i++) {
        if (urlParts[i] === 'course' && i+1 < urlParts.length) {
          courseId = urlParts[i+1];
          break;
        }
      }
      
      if(debug) console.log('Custom Linking Plugin: Found course ID from URL: ' + courseId);

      // Make AJAX call to query course-product link
      $.ajax({
        url: customLinkingData.ajax_url || '/wp-admin/admin-ajax.php',
        type: 'POST',
        data: {
          action: 'custom_linking_course_request',
          course_id: courseId,
          nonce: customLinkingData.nonce || ''
        },
        success: function(response) {
          if(debug) console.log('Custom Linking Plugin: AJAX response:', response);

          // Handle successful response
          if (response.success && response.data) {
            if (response.data.redirect_to === 'cart' && response.data.cart_url) {
              // Redirect to WooCommerce cart
              if(debug) console.log('Custom Linking Plugin: Redirecting to cart:', response.data.cart_url);
              window.location.href = response.data.cart_url;
            } else if (response.data.redirect_to === 'product' && response.data.product_url) {
              // Redirect to product page
              if(debug) console.log('Custom Linking Plugin: Redirecting to product page:', response.data.product_url);
              window.location.href = response.data.product_url;
            } else {
              if(debug) console.log('Custom Linking Plugin: No redirect needed or no linked product found');
              // If no redirection is needed, we can let the default MasterStudy behavior happen
            }
          } else {
            if(debug) console.log('Custom Linking Plugin: Invalid response format, unable to process');
          }
        },
        error: function(xhr, status, error) {
          /**
           * Enhanced error logging to provide more details about AJAX failures
           * This helps identify why the request is failing
           */
          if(debug) console.error('Custom Linking Plugin: AJAX error details:', {
            error: error,
            status: status,
            responseText: xhr.responseText,
            statusText: xhr.statusText
          });
        }
      });
      
      return false;
    });

    /**
     * Guest checkout handler - original LMS code kept for reference
     * We're intercepting the main buy button above, so this code is still here
     * but will likely not be triggered for logged-in users
     */
    $body.on('click', '[data-guest]', function (e) {
      e.preventDefault();
      var item_id = $(this).data('guest');
      var currentCart = $.cookie('stm_lms_notauth_cart');
      currentCart = typeof currentCart === 'undefined' ? [] : JSON.parse(currentCart);
      if (!currentCart.includes(item_id)) currentCart.push(item_id);
      $.cookie('stm_lms_notauth_cart', JSON.stringify(currentCart), {
        path: '/'
      });
      $.ajax({
        url: stm_lms_ajaxurl,
        dataType: 'json',
        context: this,
        data: {
          item_id: item_id,
          action: 'stm_lms_add_to_cart_guest',
          nonce: stm_lms_nonces['stm_lms_add_to_cart_guest']
        },
        beforeSend: function beforeSend() {
          $(this).addClass('loading');
        },
        complete: function complete(data) {
          data = data['responseJSON'];
          $(this).removeClass('loading');
          $(this).find('span').text(data['text']);

          if (data['cart_url']) {
            if (data['redirect']) window.location = data['cart_url'];
            $(this).attr('href', data['cart_url']).removeAttr('data-guest').addClass('goToCartUrl');
          }
        }
      });
    });
    
    /**
     * Cart URL click handler - original LMS code
     */
    $body.on('click', '.goToCartUrl', function () {
      window.location.href = $(this).attr('href');
    });
    
    // Final initialization log
    if(debug) debugLog('All button handlers initialized and ready');
    
    // Check again for buttons
    var totalButtons = 0;
    // Define button selectors that we want to match
    var buttonSelectors = [
      '#stm_lms_buy_button',
      '.get_course_button',
      '.btn:contains("GET COURSE")',
      '.stm-single-course-btn',
      '.btn-default:contains("GET COURSE")'
    ];
    
    buttonSelectors.forEach(function(selector) {
      var count = $(selector).length;
      totalButtons += count;
      if(debug && count > 0) debugLog('Found ' + count + ' buttons matching: ' + selector);
    });
    
    if(debug) debugLog('Total buttons found: ' + totalButtons);
    
    // GLOBAL FUNCTIONS for module integration
    
    // Shared AJAX processing function
    function processCourseAjax(courseId) {
      debugLog('Processing course AJAX for course: ' + courseId);
      
      // Make AJAX call to query course-product link
      $.ajax({
        url: ajaxUrl,
        type: 'POST',
        async: true,
        timeout: 15000,
        cache: false,
        data: {
          action: 'custom_linking_course_request',
          course_id: courseId,
          security: ajaxNonce
        },
        success: function(response) {
          debugLog('AJAX response received:', response);
          
          if (response && response.success === true && response.data) {
            if (response.data.redirect_to === 'cart' && response.data.cart_url) {
              debugLog('Redirecting to cart: ' + response.data.cart_url);
              setTimeout(function() {
                window.location.href = response.data.cart_url;
              }, 100);
              return;
            } else if (response.data.redirect_to === 'product' && response.data.product_url) {
              debugLog('Redirecting to product page: ' + response.data.product_url);
              setTimeout(function() {
                window.location.href = response.data.product_url;
              }, 100);
              return;
            } else {
              debugLog('No redirect needed or no linked product found');
            }
          } else {
            debugLog('Invalid response format, unable to process');
          }
        },
        error: function(xhr, status, error) {
          debugLog('AJAX error details:', {
            error: error,
            status: status,
            responseText: xhr.responseText,
            statusText: xhr.statusText
          });
        }
      });
    }
    
    // Global function to process a course (can be called by other modules)
    window.customLinkingProcessCourse = function(courseId) {
      debugLog('Processing course via global function: ' + courseId);
      
      if (!courseId) {
        debugLog('No course ID provided to global function');
        return false;
      }
      
      // Create a fake event to trigger our handler
      var fakeEvent = {
        preventDefault: function() {},
        stopPropagation: function() {}
      };
      
      // Call our handler with the course ID
      processCourseAjax(courseId);
      return true;
    };
    
    // Global function to get current course ID
    window.customLinkingGetCourseId = function() {
      return getCourseId();
    };
    
    if(debug) debugLog('Frontend JS fully initialized');
  });
})(jQuery);
