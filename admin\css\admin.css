/**
 * Custom Linking Plugin Admin Styles
 * 
 * This file contains styles for the admin interface of the Custom Linking Plugin
 * It styles the main container, form elements, and table layout
 */

/* Main container layout */
.custom-linking-admin-container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    gap: 30px;
}

/**
 * Admin form styling
 * Handles the form for adding new course-product links
 * with proper spacing and control styling
 */
.custom-linking-admin-form {
    flex: 0 0 350px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.custom-linking-admin-form h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.custom-linking-admin-form .form-group {
    margin-bottom: 15px;
}

.custom-linking-admin-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.custom-linking-admin-form select {
    width: 100%;
}

/**
 * Admin list table styling
 * Styles the table displaying existing course-product links
 * with proper spacing and header styling
 */
.custom-linking-admin-list {
    flex: 1 1 600px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.custom-linking-admin-list h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/**
 * Pagination styling
 */
.custom-linking-pagination {
    margin-top: 15px;
}

.custom-linking-pagination .page-numbers {
    display: inline-block;
    padding: 0 8px;
    height: 26px;
    line-height: 26px;
    margin: 0 2px;
    border: 1px solid #2271b1;
    border-radius: 3px;
    background: #f0f6fc;
    color: #2271b1;
    text-decoration: none;
    font-size: 13px;
    font-weight: 600;
    box-shadow: 0 1px 0 #c3c4c7;
}

.custom-linking-pagination .page-numbers.current,
.custom-linking-pagination .page-numbers:hover {
    background: #2271b1;
    color: #fff;
    border-color: #1a5e93;
}

.custom-linking-pagination .disabled {
    opacity: 0.5;
    cursor: default;
    background: #f7f7f7;
    color: #a0a5aa;
}

/**
 * Error styling for form validation
 * Styles for error states and validation messages
 */
.custom-linking-admin-form select.error {
    border-color: #d63638 !important;
    box-shadow: 0 0 0 1px #d63638 !important;
}

.custom-linking-admin-form .error-message {
    color: #d63638;
    margin-top: 5px;
    font-size: 13px;
}

.custom-linking-admin-form .error-message strong {
    font-weight: 600;
}

/**
 * Product type warning styling
 */
#product-type-error {
    color: #d63638 !important;
    margin-top: 5px !important;
    font-size: 13px !important;
    background: #fcf2f3;
    padding: 8px 12px;
    border-left: 3px solid #d63638;
    border-radius: 3px;
}

/**
 * Responsive design adjustments
 * Makes the layout responsive for different screen sizes
 */
@media screen and (max-width: 782px) {
    .custom-linking-admin-container {
        flex-direction: column;
    }
    
    .custom-linking-admin-form,
    .custom-linking-admin-list {
        flex: 1 1 100%;
    }
}
