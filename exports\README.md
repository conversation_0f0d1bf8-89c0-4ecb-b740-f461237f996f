# Custom Linking Plugin - Exports Directory

This folder stores your data when you export it before removing the plugin.

## What Happens When You Deactivate the Plugin

### Step 1: Deactivation Warning
When you try to deactivate the plugin, a popup will appear asking:
- Do you want to save your data before removing the plugin?
- You can choose "Export Data" or "Delete Without Export"

### Step 2: If You Choose Export
- Your linking data gets saved as a CSV file in this folder
- The file downloads to your computer automatically
- File name format: `custom-linking-export-2025-07-05-07-21-44.csv`

### Step 3: What Gets Exported
Your CSV file contains:
- Link ID numbers
- Course IDs and names
- Product IDs and names
- Link types (course or bundle)
- Export date and time

### Step 4: Complete Cleanup
After export (or if you skip export), the plugin removes:
- ✅ All database tables created by the plugin
- ✅ All WordPress settings saved by the plugin
- ✅ Debug log files
- ✅ Cache data

## File Storage & Cleanup

### Two Copies of Your Data
1. **Downloaded Copy**: Saved to your computer's Downloads folder (permanent)
2. **Server Copy**: Temporarily stored in this exports folder

### Automatic Cleanup (1 Hour Rule)
- Files in this folder are deleted after 1 hour
- This only happens when someone deactivates the plugin again
- Your downloaded copy is safe forever

### Important Notes for Different Environments

**If Using XAMPP (Local Development):**
- Server = Your computer running XAMPP
- Files get deleted from: `c:\xampp\htdocs\...\exports\`
- If XAMPP is turned off, cleanup doesn't happen until next deactivation

**If Using Live Website:**
- Server = Your web hosting provider
- Files get deleted from your hosting server
- Cleanup happens automatically when plugin is deactivated

## Security & Access
- This folder is protected from direct browser access
- Only the plugin can create and download files
- Files are automatically removed to prevent accumulation
- No manual cleanup needed

## File Format Example
```
"Link ID","Course ID","Product ID","Type","Course Title","Product Title"
"1","49810","49729","course","Testing Course","WordPress Course"
"7","49741","49845","bundle","Full Stack Bundle","Bundle Product"
```

## Troubleshooting

**Q: I reactivated the plugin within 1 hour. Will my export file be deleted?**
A: No! Reactivating the plugin doesn't trigger cleanup. Your file is safe.

**Q: My XAMPP was turned off for 2 hours. Is my file deleted?**
A: No! Cleanup only happens during plugin deactivation, not automatically.

**Q: I'm on a live website. When exactly are files deleted?**
A: Only when someone deactivates the plugin AND the file is older than 1 hour.

**Q: Can I manually delete files from this folder?**
A: Yes, but it's not necessary. The plugin manages this automatically.
