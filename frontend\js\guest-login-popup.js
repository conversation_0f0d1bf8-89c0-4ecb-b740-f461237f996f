(function($){
    'use strict';

    // Avoid running if user is logged in (WP passed var)
    if (typeof clpGuest === 'undefined' || clpGuest.logged_in === '1') {
        return;
    }

    var debug = false;
    if ( typeof customLinkingData !== 'undefined' && typeof customLinkingData.console_enabled !== 'undefined' ) {
        debug = !!customLinkingData.console_enabled;
    } else if ( typeof clpGuest !== 'undefined' && typeof clpGuest.console_enabled !== 'undefined' ) {
        debug = !!clpGuest.console_enabled;
    } else if ( typeof clpGuest !== 'undefined' && typeof clpGuest.debug !== 'undefined' ) {
        debug = !!clpGuest.debug;
    }
    if ( !debug ) {
        ['log','error','warn','info','debug'].forEach(function(m){ if(typeof console[m]==='function'){ console[m]=function(){}; } });
    }
    function log(){ if(!debug) return; console.log.apply(console, arguments); }

    /**
     * Intercept every click on buttons that already include
     * data-authorization-modal="login" (added by PHP filter).
     * We let MasterStudy do its own thing: it looks for that attr
     * and opens the modal itself when Bootstrap is present.
     *
     * On some sites the native handler might not be attached yet;
     * to be safe we open the modal ourselves.
     */

    // Comprehensive selector that matches all possible course and bundle buttons
    var selector = [
        // Bundle buttons
        '.mscb-single-bundle__purchase-button',
        
        // Course buttons - from custom-linking.js
        '#stm_lms_buy_button',
        '.get_course_button', 
        '.masterstudy-buy-button__link',
        '.stm-single-course-btn',
        '.btn-default[data-buy-course]',
        '.btn-default.buy-button',
        'a.btn[href*="buy-course"]',
        '.stm_lms_mixed_button__purchase',
        '.lms_purchase_button',
        'a[href*="add-to-cart"]',
        
        // Text-based selectors
        '.btn-default:contains("GET COURSE")',
        '.btn:contains("GET COURSE")',
        'a:contains("GET COURSE")',
        '.btn:contains("ENROLL")',
        'a:contains("ENROLL")',
        
        // Generic buy buttons in LMS context
        '.stm-lms-buy-buttons .btn',
        'button.lms_price_btn'
    ].join(', ');

    // Helper to bind redirection handler and remove existing ones
    function bindRedirect(){
        // Remove existing handlers to prevent conflicts
        $(selector).off('click');

        // Use capturing phase to ensure we run before any other handlers
        $(document).off('click.clpGuest', selector)
                   .on('click.clpGuest', selector, function(e){
            log('Guest redirect: Intercepting click on', this.tagName, this.className, this.textContent.trim());
            e.preventDefault();
            e.stopImmediatePropagation();
            window.location.href = clpGuest.login_url;
            return false;
        });
    }

    // Native capturing listener guarantees we run before any jQuery bubble handlers
    // Handle bundle buttons
    document.addEventListener('click', function(ev){
        var btn = ev.target.closest('.mscb-single-bundle__purchase-button');
        if(!btn) return;
        log('Capturing phase – redirecting guest to login (bundle button)');
        ev.preventDefault();
        ev.stopImmediatePropagation();
        window.location.href = clpGuest.login_url;
    }, true);
    
    // Handle course buttons with high priority capturing
    document.addEventListener('click', function(ev){
        // Check for any course button selectors
        var courseButtonSelectors = [
            '#stm_lms_buy_button',
            '.get_course_button', 
            '.masterstudy-buy-button__link',
            '.stm-single-course-btn',
            '.btn-default[data-buy-course]',
            '.btn-default.buy-button',
            '.stm_lms_mixed_button__purchase',
            '.lms_purchase_button'
        ];
        
        var btn = null;
        for(var i = 0; i < courseButtonSelectors.length; i++) {
            btn = ev.target.closest(courseButtonSelectors[i]);
            if(btn) break;
        }
        
        // Also check by text content for generic buttons
        if(!btn) {
            var target = ev.target;
            if(target.tagName === 'A' || target.tagName === 'BUTTON' || target.classList.contains('btn')) {
                var text = target.textContent.toLowerCase().trim();
                if(text.includes('get course') || text.includes('enroll') || text.includes('buy now')) {
                    btn = target;
                }
            }
        }
        
        if(!btn) return;
        
        log('Capturing phase – redirecting guest to login (course button):', btn.className, btn.textContent.trim());
        ev.preventDefault();
        ev.stopImmediatePropagation();
        window.location.href = clpGuest.login_url;
    }, true);

    // Initial bind
    bindRedirect();

    // Re-bind on ajax complete in case new buttons added
    $(document).ajaxComplete(function(){
        bindRedirect();
    });

    log('Guest login redirect initialised');

})(jQuery);