/* Basic styles for [linking] shortcode */
.vedmg-linking-container {
    margin: 20px 0;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Single button container - clean styling */
.vedmg-linking-container.single-button {
    margin: 10px 0;
    padding: 0;
    background: none;
    border-radius: 0;
    box-shadow: none;
    text-align: center;
}

.vedmg-linking-item {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

/* Single button item - clean styling */
.vedmg-linking-item.single-button {
    margin-bottom: 0;
    padding: 0;
    border: none;
    border-radius: 0;
}

/* Navigation buttons container */
.vedmg-navigation-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* Single button navigation - centered */
.vedmg-navigation-buttons.single-button {
    display: block;
    text-align: center;
    gap: 0;
}

/* Navigation button styling */
.vedmg-nav-btn {
    display: inline-block;
    padding: 12px 24px;
    background: #2271b1;
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-align: center;
    min-width: 160px;
}

.vedmg-nav-btn:hover {
    background: #1a5e93;
    color: #fff;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(34, 113, 177, 0.3);
}

.vedmg-nav-btn:active {
    transform: translateY(0);
}

/* Specific button styles */
.vedmg-home-btn {
    background: #28a745;
}

.vedmg-home-btn:hover {
    background: #218838;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.vedmg-courses-btn {
    background: #007cba;
}

.vedmg-courses-btn:hover {
    background: #005a87;
    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3);
}

.vedmg-cart-btn {
    background: #e74c3c;
}

.vedmg-cart-btn:hover {
    background: #c0392b;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* Responsive design */
@media screen and (max-width: 768px) {
    .vedmg-navigation-buttons {
        flex-direction: column;
    }
    
    .vedmg-nav-btn {
        width: 100%;
        text-align: center;
    }
    
    .vedmg-linking-container {
        padding: 15px;
        margin: 15px 0;
    }
}

/* Focus states for accessibility */
.vedmg-nav-btn:focus {
    outline: 2px solid #005cee;
    outline-offset: 2px;
}

/* Alignment options for single buttons */
.vedmg-navigation-buttons.single-button.align-left {
    text-align: left;
}

.vedmg-navigation-buttons.single-button.align-right {
    text-align: right;
}

.vedmg-linking-container.single-button.align-left {
    text-align: left;
}

.vedmg-linking-container.single-button.align-right {
    text-align: right;
}

/* Print styles */