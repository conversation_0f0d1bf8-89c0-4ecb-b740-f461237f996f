# Protect export files - only allow downloads through the plugin
<Files "*.csv">
    # Allow direct access for downloads
    Order Allow,<PERSON><PERSON>ow from all
</Files>

# Prevent directory browsing
Options -Indexes

# Cache control for export files
<IfModule mod_headers.c>
    <FilesMatch "\.(csv)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>
