<?php
/**
 * Admin page functionality for Custom Linking Plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display the admin page
 */
function custom_linking_admin_page() {
    // Check user capability
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Handle course-product link form submission
    if (isset($_POST['custom_linking_submit']) && check_admin_referer('custom_linking_save', 'custom_linking_nonce')) {
        custom_linking_handle_form_submission();
    }
    
    // Handle debug toggles submission
    if ( isset( $_POST['custom_linking_debug_submit'] ) && check_admin_referer( 'custom_linking_debug_save', 'custom_linking_debug_nonce' ) ) {
        // Save console toggle
        $console_enabled = isset( $_POST['enable_console'] ) ? 1 : 0;
        update_option( 'custom_linking_enable_console', $console_enabled );
        // Save debug log toggle
        $debug_log_enabled = isset( $_POST['enable_debug_log'] ) ? 1 : 0;
        update_option( 'custom_linking_enable_debug_log', $debug_log_enabled );
        // Redirect to avoid re-POST
        wp_redirect( add_query_arg( 'message', 'debug_saved', admin_url( 'admin.php?page=custom-linking-plugin' ) ) );
        exit;
    }

    // Handle delete action
    if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id']) && check_admin_referer('custom_linking_delete_' . $_GET['id'])) {
        custom_linking_delete_link((int)$_GET['id']);
    }
    
    // Current debug option values for form display
    $console_enabled     = (bool) get_option( 'custom_linking_enable_console', false );
    $debug_log_enabled   = (bool) get_option( 'custom_linking_enable_debug_log', false );

    // Search query for existing links
    $search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';
    
    // Pagination setup for existing links
    $per_page   = 10; // items per page
    $paged      = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
    $offset     = ($paged - 1) * $per_page;

    // Fetch total count & current page items (with search filter)
    $total_links  = custom_linking_count_links($search);
    $total_pages  = (int) ceil($total_links / $per_page);
    $links        = custom_linking_get_all_links($per_page, $offset, $search);
    
    // Get courses and products for dropdown
    $courses = custom_linking_get_available_courses();
    $products = custom_linking_get_available_products();
    
    // Display admin UI
    ?>
    <div class="wrap">
        <h1><?php echo esc_html__('Course-Product Linking', 'custom-linking-plugin'); ?></h1>
        
        <?php if (isset($_GET['message']) && $_GET['message'] === 'success'): ?>
            <div class="notice notice-success is-dismissible">
                <p><?php echo esc_html__('Course connection saved successfully', 'custom-linking-plugin'); ?></p>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_GET['message']) && $_GET['message'] === 'deleted'): ?>
            <div class="notice notice-success is-dismissible">
                <p><?php echo esc_html__('Course connection deleted successfully', 'custom-linking-plugin'); ?></p>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_GET['message']) && $_GET['message'] === 'debug_saved'): ?>
            <div class="notice notice-success is-dismissible">
                <p><?php echo esc_html__('Debug settings saved', 'custom-linking-plugin'); ?></p>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_GET['message']) && $_GET['message'] === 'error'): ?>
            <div class="notice notice-error is-dismissible">
                <p><?php echo esc_html__('Error occurred', 'custom-linking-plugin'); ?></p>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_GET['message']) && $_GET['message'] === 'invalid_product_type'): ?>
            <div class="notice notice-error is-dismissible">
                <p><?php echo esc_html__('Error: Only simple products can be linked to courses or bundles. Please select a simple product type.', 'custom-linking-plugin'); ?></p>
            </div>
        <?php endif; ?>
        
        <div class="custom-linking-admin-container">
            <div class="custom-linking-admin-form">
                <h2><?php echo esc_html__('Add New Course connection(LMS to Woocommerce)', 'custom-linking-plugin'); ?></h2>
                <form method="post" action="">
                    <?php wp_nonce_field('custom_linking_save', 'custom_linking_nonce'); ?>
                    
                    <div class="form-group">
                        <label for="course_id">
                            <?php echo esc_html__('LMS Course Name', 'custom-linking-plugin'); ?>:
                        </label>
                        <select name="course_id" id="course_id" class="regular-text" required>
                            <option value=""><?php echo esc_html__('-- Select LMS Course --', 'custom-linking-plugin'); ?></option>
                            <?php foreach ($courses as $course): ?>
                                <option value="<?php echo esc_attr($course->ID); ?>">
                                    <?php echo esc_html($course->post_title); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="product_id">
                            <?php echo esc_html__('Woocommerce Course Name', 'custom-linking-plugin'); ?>:
                        </label>
                        <select name="product_id" id="product_id" class="regular-text" required>
                            <option value=""><?php echo esc_html__('-- Select Woocommerce Product (Simple Products Only) --', 'custom-linking-plugin'); ?></option>
                            <?php foreach ($products as $product): ?>
                                <option value="<?php echo esc_attr($product->ID); ?>">
                                    <?php echo esc_html($product->post_title); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description"><?php echo esc_html__('Note: Only simple product types can be linked to courses or bundles.', 'custom-linking-plugin'); ?></p>
                    </div>
                    
                    <div class="form-group">
                        <label for="type">
                            <?php echo esc_html__('Type', 'custom-linking-plugin'); ?>:
                        </label>
                        <select name="type" id="type" class="regular-text" required>
                            <option value="course"><?php echo esc_html__('Course', 'custom-linking-plugin'); ?></option>
                            <option value="bundle"><?php echo esc_html__('Bundle', 'custom-linking-plugin'); ?></option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <input type="submit" name="custom_linking_submit" class="button button-primary" value="<?php echo esc_attr__('Save Course Connection', 'custom-linking-plugin'); ?>"> 
                    </div>
                </form>
            </div>
            
            <!-- Debug Settings -->
            <div class="custom-linking-debug-settings" style="margin-top:30px;">
                <h2><?php echo esc_html__('Debug Settings', 'custom-linking-plugin'); ?></h2>
                <form method="post" action="">
                    <?php wp_nonce_field('custom_linking_debug_save', 'custom_linking_debug_nonce'); ?>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php esc_html_e('Enable Console Debug', 'custom-linking-plugin'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_console" value="1" <?php checked( $console_enabled ); ?> />
                                    <?php esc_html_e('Log messages to browser console', 'custom-linking-plugin'); ?>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php esc_html_e('Enable Debug Log', 'custom-linking-plugin'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_debug_log" value="1" <?php checked( $debug_log_enabled ); ?> />
                                    <?php esc_html_e('Write messages to plugin debug.log file', 'custom-linking-plugin'); ?>
                                </label>
                            </td>
                        </tr>
                    </table>
                    <p>
                        <input type="submit" name="custom_linking_debug_submit" class="button button-secondary" value="<?php esc_attr_e( 'Save Debug Settings', 'custom-linking-plugin' ); ?>" />
                    </p>
                </form>
            </div>

            <div class="custom-linking-admin-list">
                <h2><?php echo esc_html__('Existing Course Connections', 'custom-linking-plugin'); ?></h2>

                <!-- Search form -->
                <form method="get" action="" class="custom-linking-search-form" style="margin-bottom:10px;">
                    <input type="hidden" name="page" value="custom-linking-plugin" />
                    <input type="text" name="s" value="<?php echo esc_attr( $search ); ?>" placeholder="<?php esc_attr_e('Search course connections...', 'custom-linking-plugin'); ?>" />
                    <input type="submit" class="button" value="<?php esc_attr_e('Search', 'custom-linking-plugin'); ?>" />
                </form>
                
                <?php if (empty($links)): ?>
                    <div class="notice notice-info">
                        <p><?php echo esc_html__('No course connections found. Add your first LMS to WooCommerce course connection above.', 'custom-linking-plugin'); ?></p>
                    </div>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php echo esc_html__('ID', 'custom-linking-plugin'); ?></th>
                                <th><?php echo esc_html__('LMS Course Name', 'custom-linking-plugin'); ?></th>
                                <th><?php echo esc_html__('Woocommerce Course Name', 'custom-linking-plugin'); ?></th>
                                <th><?php echo esc_html__('Type', 'custom-linking-plugin'); ?></th>
                                <th><?php echo esc_html__('Actions', 'custom-linking-plugin'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($links as $link): ?>
                                <tr>
                                    <td><?php echo esc_html($link->id); ?></td>
                                    <td>
                                        <?php 
                                        $course_title = get_the_title($link->course_id);
                                        echo $course_title ? esc_html($course_title) : esc_html__('LMS Course not found', 'custom-linking-plugin') . ' (ID: ' . esc_html($link->course_id) . ')';
                                        ?>
                                    </td>
                                    <td>
                                        <?php 
                                        $product_title = get_the_title($link->product_id);
                                        echo $product_title ? esc_html($product_title) : esc_html__('Woocommerce Product not found', 'custom-linking-plugin') . ' (ID: ' . esc_html($link->product_id) . ')';
                                        ?>
                                    </td>
                                    <td><?php echo esc_html($link->type); ?></td>
                                    <td>
                                        <?php
                                        $delete_url = add_query_arg(
                                            array(
                                                'page' => 'custom-linking-plugin',
                                                'action' => 'delete',
                                                'id' => $link->id,
                                                '_wpnonce' => wp_create_nonce('custom_linking_delete_' . $link->id),
                                            ),
                                            admin_url('admin.php')
                                        );
                                        ?>
                                        <a href="<?php echo esc_url($delete_url); ?>" class="button button-small" 
                                           onclick="return confirm('<?php echo esc_js(__('Are you sure you want to delete this course connection?', 'custom-linking-plugin')); ?>')">
                                            <?php echo esc_html__('Delete', 'custom-linking-plugin'); ?>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <?php
                    // Render pagination controls under the table when needed
                    if ($total_pages > 1) {
                        // Build pagination links with first/last controls
                            $pagination_links = paginate_links( array(
                                'base'      => add_query_arg( array( 'paged' => '%#%', 's' => urlencode( $search ) ) ),
                                'format'    => '',
                                'current'   => $paged,
                                'total'     => $total_pages,
                                'prev_text' => __('« Prev', 'custom-linking-plugin'),
                                'next_text' => __('Next »', 'custom-linking-plugin'),
                                'type'      => 'array',          // return as array so we can modify
                                'end_size'  => 1,
                                'mid_size'  => 1,
                            ) );

                            if ( $pagination_links ) {
                                // First & Last links
                                $first_link = ( $paged > 1 ) ? '<a class="page-numbers first-page" href="' . esc_url( remove_query_arg( 'paged' ) ) . '">« ' . __( 'First', 'custom-linking-plugin' ) . '</a>' : '<span class="page-numbers first-page disabled">« ' . __( 'First', 'custom-linking-plugin' ) . '</span>';
                                $last_link  = ( $paged < $total_pages ) ? '<a class="page-numbers last-page" href="' . esc_url( add_query_arg( array( 'paged' => $total_pages ) ) ) . '">' . __( 'Last', 'custom-linking-plugin' ) . ' »</a>' : '<span class="page-numbers last-page disabled">' . __( 'Last', 'custom-linking-plugin' ) . ' »</span>';

                                array_unshift( $pagination_links, $first_link );
                                array_push( $pagination_links, $last_link );

                                echo '<div class="tablenav bottom"><div class="tablenav-pages custom-linking-pagination">' .
                                    implode( ' ', $pagination_links ) .
                                    '</div></div>';
                            }
                    }
                    ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Handle form submission to save a new course-product link
 */
function custom_linking_handle_form_submission() {
    // Validate and sanitize input
    $course_id = isset($_POST['course_id']) ? (int) $_POST['course_id'] : 0;
    $product_id = isset($_POST['product_id']) ? (int) $_POST['product_id'] : 0;
    $type = isset($_POST['type']) && in_array($_POST['type'], array('course', 'bundle')) ? sanitize_text_field($_POST['type']) : 'course';
    
    // Validate required fields
    if (!$course_id || !$product_id) {
        // Redirect with error
        wp_redirect(add_query_arg('message', 'error', admin_url('admin.php?page=custom-linking-plugin')));
        exit;
    }
    
    // PRODUCT TYPE VALIDATION: Only allow simple products to be linked
    if (function_exists('wc_get_product')) {
        $product = wc_get_product($product_id);
        if (!$product || $product->get_type() !== 'simple') {
            // Redirect with specific error for non-simple products
            wp_redirect(add_query_arg('message', 'invalid_product_type', admin_url('admin.php?page=custom-linking-plugin')));
            exit;
        }
    }
    
    // Save to database
    $result = link_product_to_course($course_id, $product_id, $type);
    
    // Redirect with appropriate message
    $message = $result ? 'success' : 'error';
    wp_redirect(add_query_arg('message', $message, admin_url('admin.php?page=custom-linking-plugin')));
    exit;
}

/**
 * Delete a course-product link
 *
 * @param int $id The link ID to delete
 */
function custom_linking_delete_link($id) {
    // Delete from database
    $result = unlink_product_from_course_by_id($id);
    
    // Redirect with appropriate message
    $message = $result ? 'deleted' : 'error';
    wp_redirect(add_query_arg('message', $message, admin_url('admin.php?page=custom-linking-plugin')));
    exit;
}

/**
 * Get all course-product links from the database
 *
 * @return array Array of link objects
 */
function custom_linking_get_all_links($limit = 0, $offset = 0, $search = '') {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
        // Base query with joins to allow searching by post titles
    $sql = "SELECT l.*
            FROM $table_name l
            LEFT JOIN {$wpdb->posts} c ON c.ID = l.course_id
            LEFT JOIN {$wpdb->posts} p ON p.ID = l.product_id";

    // Build WHERE clause when searching
    $where = array();
    if ( $search !== '' ) {
                if ( ctype_digit( $search ) ) {
            // Numeric search: match exact IDs
            $where[] = $wpdb->prepare( '( l.id = %d OR l.course_id = %d OR l.product_id = %d )', intval( $search ), intval( $search ), intval( $search ) );
        } else {
            // Text search: match titles with LIKE
            $like = '%' . $wpdb->esc_like( $search ) . '%';
            $where[] = $wpdb->prepare( '( c.post_title LIKE %s OR p.post_title LIKE %s )', $like, $like );
        }
    }
    if ( $where ) {
        $sql .= ' WHERE ' . implode( ' AND ', $where );
    }

    $sql .= ' ORDER BY l.id DESC';

    // Add LIMIT clause when pagination parameters are provided
    if ($limit > 0) {
        // Prepare ensures proper integer values are used
        $sql .= $wpdb->prepare(" LIMIT %d OFFSET %d", $limit, $offset);
    }

    $links = $wpdb->get_results($sql);
    
    return $links ?: array();
}

/**
 * Count total course-product links
 *
 * @return int Total number of links
 */
function custom_linking_count_links($search = '') {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';

        // Similar search-aware count query
    $sql = "SELECT COUNT(*) FROM $table_name l
            LEFT JOIN {$wpdb->posts} c ON c.ID = l.course_id
            LEFT JOIN {$wpdb->posts} p ON p.ID = l.product_id";
    if ( $search !== '' ) {
                if ( ctype_digit( $search ) ) {
        $sql .= $wpdb->prepare( ' WHERE ( l.id = %d OR l.course_id = %d OR l.product_id = %d )', intval( $search ), intval( $search ), intval( $search ) );
    } else {
        $like = '%' . $wpdb->esc_like( $search ) . '%';
        $sql .= $wpdb->prepare( ' WHERE ( c.post_title LIKE %s OR p.post_title LIKE %s )', $like, $like );
    }
    }
    return (int) $wpdb->get_var( $sql );
}

/**
 * Get available MasterStudy LMS courses
 *
 * @return array Array of course post objects
 */
function custom_linking_get_available_courses() {
    // Check if MasterStudy LMS is active and has the stm-lms-course post type
    if (!post_type_exists('stm-courses') && !post_type_exists('mscb_bundle')) {
        return array();
    }
    
    return get_posts(array(
        'post_type' => array('stm-courses', 'mscb_bundle'),
        'post_status' => 'publish',
        'numberposts' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
    ));
}

/**
 * Get available WooCommerce products (only simple products)
 *
 * @return array Array of product post objects
 */
function custom_linking_get_available_products() {
    // Check if WooCommerce is active and has the product post type
    if (!post_type_exists('product')) {
        return array();
    }
    
    // Get all published products
    $all_products = get_posts(array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'numberposts' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
    ));
    
    // Filter to only include simple products
    $simple_products = array();
    foreach ($all_products as $product_post) {
        $product = wc_get_product($product_post->ID);
        if ($product && $product->get_type() === 'simple') {
            $simple_products[] = $product_post;
        }
    }
    
    return $simple_products;
}

/**
 * Get product types for JavaScript validation
 *
 * @return array Array mapping product IDs to their types
 */
function custom_linking_get_product_types_for_js() {
    if (!function_exists('wc_get_product') || !post_type_exists('product')) {
        return array();
    }
    
    $product_types = array();
    
    // Get all published products (not just simple ones for validation purposes)
    $all_products = get_posts(array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'numberposts' => -1,
        'fields' => 'ids', // Only get IDs for better performance
    ));
    
    foreach ($all_products as $product_id) {
        $product = wc_get_product($product_id);
        if ($product) {
            $product_types[$product_id] = $product->get_type();
        }
    }
    
    return $product_types;
}