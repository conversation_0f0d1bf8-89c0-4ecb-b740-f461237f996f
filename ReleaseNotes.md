# 🔗 Custom Linking Plugin - Release Notes
**Version:** 1.1.0 | **Author:** Ku<PERSON><PERSON> Mishra

## 📋 Table of Contents
1. [Overview](#overview)
2. [Core Features](#core-features)
3. [Required Plugins](#required-plugins)
4. [Installation](#installation)
5. [Usage Guide](#usage-guide)
6. [Technical Documentation](#technical-documentation)
7. [Advanced Features](#advanced-features)
8. [File Structure](#file-structure)
9. [Troubleshooting](#troubleshooting)

---

## 📝 Overview
Enterprise-level solution that integrates **MasterStudy LMS** courses with **WooCommerce** products, enabling course purchases through WooCommerce's checkout system with automatic course access after purchase.

**Key Benefits:**
- Seamless course-product linking (individual courses and bundles)
- Guest user management with login redirection
- Free course enrollment handling
- Automatic order completion processing
- Comprehensive admin panel with search and pagination
- Complete deactivation system with data export

---

## 🚀 Core Features

### 1. **Course-Product Linking**
- Links MasterStudy LMS courses/bundles to WooCommerce products
- Custom database table (`wp_linking_table`) for relationships
- Support for 'course' and 'bundle' types

### 2. **Guest User Management**
**Files:** `frontend/guestLoginHandler.php`, `frontend/js/guest-login-popup.js`
- Auto-redirects non-logged users to login page (`/user-account/`)
- High-priority click interception with comprehensive button coverage

### 3. **Free Course Handling**
**Files:** `frontend/FreeCourse.php`, `frontend/js/FreeCourse.js`
- **Linked Free Courses:** Follow normal purchase flow
- **Non-Linked Free Courses:** Direct enrollment for logged-in users

### 4. **Order Completion & Access**
**Files:** `frontend/order-completion.php`, `frontend/bundle-completion.php`
- Hooks: `woocommerce_order_status_completed`, `woocommerce_order_status_processing`
- Bundle processing with multiple fallback methods
- Duplicate prevention mechanisms

### 5. **Admin Management Panel**
**Files:** `admin/linkAdmin.php`, `admin/admin.php`, CSS/JS files
- Create and manage course-product links
- Search by ID, course title, or product title
- Pagination (10 items per page)
- Debug settings with console/file logging toggles

### 6. **Complete Deactivation System**
**Files:** `database/Deactivator.php`, `admin/deactivationHandler.php`, JS/CSS files
- Interactive confirmation modal
- Optional CSV data export before deletion
- Complete cleanup: database tables, options, files
- Automatic export file cleanup after 1 hour

---

## 🔌 Required Plugins

1. **MasterStudy LMS Learning Management System** - Core LMS functionality
2. **WooCommerce** - Payment processing and product management
3. **Course Bundler Plugin** - Bundle functionality (optional)

---

## 💻 Installation

1. Upload plugin folder to `/wp-content/plugins/`
2. Activate through WordPress 'Plugins' menu
3. Verify WooCommerce and MasterStudy LMS are active
4. Navigate to **Custom Linking > Products & Courses**

---

## 📘 Usage Guide

### Creating Links
1. Go to **Custom Linking > Products & Courses**
2. Click **Add New Link**
3. Select course and type ('course' or 'bundle')
4. Click **Save Link**

### Managing Links
- **Search:** Find by ID, course name, or product name
- **Pagination:** Navigate through links
- **Delete:** Remove with confirmation

### Debug Settings
- **Console Debug:** Show/hide browser console output
- **Debug Log:** Write to `debug.log` file

### Frontend Behavior
- **Logged Users:** Redirected to WooCommerce for purchase
- **Guest Users:** Redirected to login page
- **Free Courses:** Direct enrollment or WooCommerce flow based on linking

---

## 🔧 Technical Documentation

### Database Structure
```sql
CREATE TABLE wp_linking_table (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    course_id mediumint(9) NOT NULL,
    product_id mediumint(9) NOT NULL,
    type varchar(20) NOT NULL,
    PRIMARY KEY (id),
    KEY product_id (product_id)
)
```

### Core Mechanisms

**Button Override System:**
- JavaScript intercepts MasterStudy LMS button clicks
- Checks login status and enrollment before processing
- AJAX calls determine course-product relationships

**Guest Redirection:**
- High-priority JavaScript captures clicks for non-logged users
- Immediate redirect to configured login URL

**Order Processing:**
- Analyzes order items for linked courses/bundles
- Multiple verification layers prevent duplicates
- Uses various MasterStudy LMS enrollment methods

**Bundle Processing:**
- Native Course Bundler enrollment when available
- Multiple fallback methods for course discovery
- Individual course processing with verification

### AJAX Endpoints
- `custom_linking_course_request`: Course purchase requests
- `custom_linking_process_bundle`: Bundle purchase requests
- `custom_linking_process_free_course`: Free course access
- Security: WordPress nonce verification, capability checks

### Deactivation Process
1. User clicks deactivate → JavaScript intercepts
2. Data count check → AJAX request
3. Confirmation modal → Warning and export options
4. Optional export → CSV creation and download
5. Complete cleanup → Remove all traces
6. WordPress deactivation completion

**What Gets Removed:**
- Database tables: `wp_linking_table`
- WordPress options: All plugin-specific settings
- Files: Debug logs, temporary files
- Cache: WordPress cache cleared

---

## ⚡ Advanced Features

### 1. **Duplicate Prevention System**
- Multiple verification layers before enrollment
- 8 different access checking strategies
- Track processed courses per order
- Prioritize bundle access over individual access

### 2. **Product Type Filtering**
Default: Simple products only. To modify:

**Server-side (`admin/admin.php`):**
```php
$allowed_types = array('simple', 'variable', 'grouped');
```

**Client-side (`admin/js/admin.js`):**
```javascript
var allowedTypes = ['simple', 'variable', 'grouped'];
```

### 3. **Edge Case Handling**
- Bundle child product filtering
- Guest user button conflicts resolution
- WordPress course timestamp issues
- Multiple enrollment detection strategies

### 4. **Environment Support**
- Development vs production configurations
- Configurable login URLs
- Cross-browser compatibility
- XAMPP localhost and live server support

---

## 📂 File Structure

### **Core Files**
- `custom-linking-plugin.php` - Main plugin initialization
- `class-core.php` - Core class management

### **Admin Interface** (`admin/`)
- `linkAdmin.php` - Menu registration and page setup
- `admin.php` - Main admin functionality
- `deactivationHandler.php` - AJAX handlers for deactivation
- `css/admin.css` - Admin styling
- `js/admin.js` - Admin JavaScript
- `js/deactivation.js` - Deactivation modal interface

### **Frontend** (`frontend/`)
- `linkFrontend.php` - Main frontend initialization
- `courselinking.php` - AJAX course-product queries
- `guestLoginHandler.php` - Guest redirect configuration
- `FreeCourse.php` - Free course logic
- `bundleLinkFrontend.php` - Bundle initialization
- `bundleLinking.php` - Bundle AJAX handler
- `order-completion.php` - Order processing
- `bundle-completion.php` - Bundle order processing
- `js/` - Frontend JavaScript files

### **Database** (`database/`)
- `Activator.php` - Plugin activation
- `Deactivator.php` - Plugin deactivation and cleanup
- `linkDatabase.php` - Database operations

### **Utilities** (`includes/`)
- `debug.php` - Debug logging system
- `integration.php` - Shared integration functions
- `linkIncludes.php` - Common utilities

### **Shortcode** (`shortcode/`)
- `shortcode.php` - Shortcode functionality
- CSS/JS files

### **Exports** (`exports/`)
- Temporary CSV storage
- `.htaccess` protection
- `README.md` - Export documentation

---

## 🛠️ Troubleshooting

### Common Issues

**Q: Course buttons not redirecting to WooCommerce**
A: Check if course-product link exists and debug logging is enabled

**Q: Bundle enrollment not working**
A: Verify Course Bundler plugin is active and courses are properly linked

**Q: Guest users not redirecting to login**
A: Check login URL configuration (`/user-account/`)

**Q: Free courses not enrolling directly**
A: Verify course is not linked to a product for direct enrollment

**Q: Export files not being cleaned up**
A: Cleanup only occurs during plugin deactivation, not automatically

### Debug Steps
1. Enable debug logging in admin panel
2. Check `debug.log` in plugin root directory
3. Use browser console for frontend issues
4. Verify all required plugins are active

### File Permissions
Ensure WordPress can write to:
- Plugin directory for debug logs
- `exports/` folder for CSV files
- Database for table operations

---

**Support:** For technical support, check debug logs and verify all dependencies are properly installed and configured.
