/**
 * Free Course Handler - Frontend JavaScript
 * 
 * This file handles the frontend logic for free courses:
 * - Detects if a course is free
 * - Handles the flow for free courses (linked vs unlinked)
 * - Manages direct enrollment for unlinked free courses
 */

(function ($) {
    'use strict';

    // Debug logging with console silencing support
    var debug = false;
    // Allow WordPress to enable this via localized script variable
    if (typeof customFreeCourseData !== 'undefined' && typeof customFreeCourseData.console_enabled !== 'undefined') {
        debug = !!customFreeCourseData.console_enabled;
    }
    // If debugging is off, silence console output from this script
    if (!debug) {
        ['log','error','warn','info','debug'].forEach(function(m){
            if (typeof console[m] === 'function') {
                console[m] = function(){};
            }
        });
    }

    function debugLog(message, data) {
        if (debug && console && console.log) {
            console.log('[Free Course] ' + message, data || '');
        }
    }

    // Check if we have required data
    if (typeof customFreeCourseData === 'undefined') {
        debugLog('customFreeCourseData not found - free course functionality may not work properly');
        return;
    }

    var ajaxUrl = customFreeCourseData.ajax_url;
    var nonce = customFreeCourseData.nonce;    /**
     * Check if a course appears to be free based on DOM elements
     * This is a client-side check to complement server-side validation
     */
    function isCourseFreeDOMCheck() {
        // Look for price indicators in the course page
        var priceElements = $('.stm_lms_course__price, .course-price, .price, .stm-lms-course-price, .price-value');
        var isFree = false;

        priceElements.each(function() {
            var priceText = $(this).text().toLowerCase().trim();
            if (priceText.includes('free') || 
                priceText.includes('$0') || 
                priceText.includes('0.00') ||
                priceText === '0' ||
                priceText === '' ||
                priceText.includes('free course') ||
                priceText.includes('no cost')) {
                isFree = true;
                return false; // Break the loop
            }
        });

        // Also check for specific free course indicators
        if ($('.free-course, .course-free, .stm-course-free, .price-free').length > 0) {
            isFree = true;
        }

        // Check if any element specifically says "Free"
        if ($('*:contains("Free"):not(script):not(style)').filter(function() {
            return $(this).children().length === 0 && $(this).text().toLowerCase().trim() === 'free';
        }).length > 0) {
            isFree = true;
        }

        debugLog('DOM free course check result:', isFree);
        return isFree;
    }

    /**
     * Extract course ID from various possible sources
     */
    function getCourseId() {
        var courseId = null;

        // Try to get from data attributes
        if ($('[data-course-id]').length) {
            courseId = $('[data-course-id]').first().data('course-id');
        }

        // Try to get from URL or other sources
        if (!courseId && window.stm_lms_course_id) {
            courseId = window.stm_lms_course_id;
        }

        // Try to get from body class
        if (!courseId) {
            var bodyClasses = $('body').attr('class');
            if (bodyClasses) {
                var match = bodyClasses.match(/postid-(\d+)/);
                if (match) {
                    courseId = match[1];
                }
            }
        }

        debugLog('Extracted course ID:', courseId);
        return courseId;
    }

    /**
     * Process free course request
     */
    function processFreeCart(courseId) {
        debugLog('Processing free course:', courseId);

        if (!courseId) {
            debugLog('No course ID provided for free course processing');
            return;
        }

        // Show loading state
        var $button = $('.stm_lms_buy_button, .course-buy-button, [data-course-buy]');
        var originalText = $button.text();
        $button.text('Processing...').prop('disabled', true);

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'custom_linking_process_free_course',
                course_id: courseId,
                nonce: nonce
            },            success: function(response) {
                debugLog('Free course AJAX response:', response);

                if (response.success) {
                    var data = response.data;
                    
                    if (data.redirect_to === 'login' && data.login_url) {
                        // Redirect to login
                        debugLog('Redirecting to login:', data.login_url);
                        showMessage('Please log in to access this free course.', 'info');
                        setTimeout(function() {
                            window.location.href = data.login_url;
                        }, 1000);
                          } else if (data.redirect_to === 'course' && data.course_url) {
                        // Redirect to course (direct access granted)
                        debugLog('Redirecting to course:', data.course_url);
                        
                        if (data.newly_enrolled) {
                            // Show success message before redirect
                            showMessage('Success! You have been enrolled in this free course.', 'success');
                            setTimeout(function() {
                                window.location.href = data.course_url;
                            }, 1500);
                        } else if (data.already_enrolled) {
                            showMessage('You already have access to this course.', 'info');
                            setTimeout(function() {
                                window.location.href = data.course_url;
                            }, 1000);
                        } else if (data.enrollment_warning) {
                            // Show warning but still redirect
                            showMessage(data.warning_message || 'Free course access granted.', 'warning');
                            setTimeout(function() {
                                window.location.href = data.course_url;
                            }, 2000);
                        } else {
                            window.location.href = data.course_url;
                        }
                        
                    } else if (data.redirect_to === 'cart' && data.cart_url) {
                        // Free course linked to product - redirect to cart
                        debugLog('Redirecting to cart:', data.cart_url);
                        showMessage('Free course added to cart. Redirecting to checkout...', 'success');
                        setTimeout(function() {
                            window.location.href = data.cart_url;
                        }, 1000);
                        
                    } else {
                        // Unknown redirect type - show message and reload page
                        debugLog('Unknown redirect type:', data.redirect_to);
                        showMessage('Course processed successfully. Please refresh the page.', 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    }
                } else {
                    // Error occurred
                    var errorData = response.data || {};
                    
                    if (errorData.redirect_to_normal) {
                        // Course is not free, fall back to normal processing
                        debugLog('Course is not free, falling back to normal processing');
                        processNormalCourse(courseId);
                    } else {
                        // Show specific error message
                        var errorMessage = errorData.message || 'Failed to process free course';
                        debugLog('Free course processing failed:', errorMessage);
                        
                        // Enhanced error messages
                        if (errorData.error === 'enrollment_failed') {
                            errorMessage = 'Unable to enroll in this free course. Please try again or contact support.';
                        } else if (errorData.error === 'cart_add_failed') {
                            errorMessage = 'Unable to add course to cart. Please try again.';
                        } else if (errorData.error === 'product_not_found') {
                            errorMessage = 'Course configuration error. Please contact support.';
                        }
                        
                        showMessage(errorMessage, 'error');
                        
                        // Log detailed error for debugging
                        if (errorData.detailed_error) {
                            debugLog('Detailed error:', errorData.detailed_error);
                        }
                    }
                }
            },
            error: function(xhr, status, error) {
                debugLog('Free course AJAX error:', {xhr: xhr, status: status, error: error});
                showMessage('An error occurred while processing the course', 'error');
            },
            complete: function() {
                // Restore button state
                $button.text(originalText).prop('disabled', false);
            }
        });
    }

    /**
     * Fall back to normal course processing
     */
    function processNormalCourse(courseId) {
        debugLog('Falling back to normal course processing for course:', courseId);
        
        // Trigger the normal course linking process
        if (typeof customLinkingData !== 'undefined' && window.customLinkingProcessCourse) {
            window.customLinkingProcessCourse(courseId);
        } else {
            // Make AJAX call to normal course processing using the main plugin's endpoint
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'custom_linking_course_request', // Use main plugin's action
                    course_id: courseId,
                    security: nonce // Use security instead of nonce for compatibility
                },
                success: function(response) {
                    debugLog('Normal course processing response:', response);
                    // Handle normal course response (redirect to product/cart)
                    if (response.success && response.data) {
                        var data = response.data;
                        if (data.redirect_to === 'cart' && data.cart_url) {
                            window.location.href = data.cart_url;
                        } else if (data.redirect_to === 'product' && data.product_url) {
                            window.location.href = data.product_url;
                        }
                    }
                },
                error: function(xhr, status, error) {
                    debugLog('Normal course processing error:', error);
                    showMessage('An error occurred while processing the course', 'error');
                }
            });
        }
    }    /**
     * Show message to user
     */
    function showMessage(message, type) {
        // Remove existing messages
        $('.free-course-message').remove();
        
        // Create message element
        var messageClass = 'free-course-message';
        if (type === 'success') {
            messageClass += ' notice notice-success';
        } else if (type === 'error') {
            messageClass += ' notice notice-error';
        } else if (type === 'warning') {
            messageClass += ' notice notice-warning';
        } else if (type === 'info') {
            messageClass += ' notice notice-info';
        }
        
        var $message = $('<div class="' + messageClass + '" style="padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; background: #f7f7f7;"><p>' + message + '</p></div>');
        
        // Add specific styling based on type
        if (type === 'success') {
            $message.css('border-left-color', '#46b450');
        } else if (type === 'error') {
            $message.css('border-left-color', '#dc3232');
        } else if (type === 'warning') {
            $message.css('border-left-color', '#ffb900');
        }
        
        // Find a good place to insert the message
        var $target = $('.stm_lms_course_content, .course-content, .entry-content').first();
        if ($target.length === 0) {
            $target = $('body');
        }
        
        $message.prependTo($target);
        
        // Auto-remove after 5 seconds (longer for warnings)
        var timeout = (type === 'warning') ? 7000 : 5000;
        setTimeout(function() {
            $message.fadeOut(function() {
                $message.remove();
            });
        }, timeout);
    }    /**
     * Check if user is already enrolled in the course
     * This prevents interference with "Start Course" and other post-enrollment buttons
     */
    function isUserEnrolledInCourse() {
        debugLog('Checking if user is enrolled in course...');
        
        // CRITICAL: Check if body has enrolled class (set by PHP)
        if ($('body').hasClass('custom-linking-user-enrolled')) {
            debugLog('Body has enrolled class - user is definitely enrolled');
            return true;
        }
        
        // Strategy 1: Check for various enrollment indicators in the DOM
        var enrollmentIndicators = [
            '.course-enrolled', '.user-enrolled', '.already-enrolled', '.enrollment-status',
            '.stm-course-enrolled', '.course-access-granted', '.student-enrolled',
            '[data-enrolled="true"]', '[data-user-enrolled="true"]', '.enrolled-course',
            '.stm_lms_progress_bar', '.stm-lms-progress', '.course-progress'
        ];
        
        for (var i = 0; i < enrollmentIndicators.length; i++) {
            if ($(enrollmentIndicators[i]).length > 0) {
                debugLog('Found enrollment indicator:', enrollmentIndicators[i]);
                return true;
            }
        }
        
        // Strategy 2: Check for "Start Course" button presence (most reliable indicator)
        var startCourseButtons = $(
            'a[href*="/lesson/"], a[href*="/curriculum/"], ' +  // Links to lessons/curriculum
            'a[href*="lesson_id"], a[href*="curriculum_id"], ' +
            '.stm-lms-course-navigation, .stm-course-navigation, ' +
            '.start-course, .continue-course, .resume-course, ' +
            '.take-course-btn, .begin-course-btn, .stm-course-start-btn, .course-start-button'
        );
        
        // Also check by text content for start/continue buttons
        var buttonsByText = $('a, button').filter(function() {
            var buttonText = $(this).text().toLowerCase().trim();
            var href = $(this).attr('href') || '';
            
            // Check if the text indicates this is a start/continue button
            var isStartButton = (
                buttonText.includes('start course') || 
                buttonText.includes('continue course') || 
                buttonText.includes('resume course') ||
                buttonText.includes('take course') ||
                buttonText.includes('begin course') ||
                buttonText.includes('go to course')
            );
            
            // Check if the href indicates access to course content
            var hasContentAccess = (
                href.includes('/lesson/') || 
                href.includes('/curriculum/') ||
                href.includes('lesson_id') ||
                href.includes('curriculum_id')
            );
            
            return isStartButton || hasContentAccess;
        });
        
        var allStartButtons = startCourseButtons.add(buttonsByText);
        
        if (allStartButtons.length > 0) {
            debugLog('Found start course buttons - user appears to be enrolled');
            debugLog('Start buttons found:', allStartButtons.length);
            
            // IMPORTANT: Mark this globally to prevent any further interference
            $('body').addClass('custom-linking-user-enrolled');
            window.customFreeCourseUserEnrolled = true;
            
            return true;
        }
        
        // Strategy 3: Check if there are NO "Get Course"/"Buy" buttons (indicates enrollment)
        var buyButtons = $(
            '#stm_lms_buy_button, .get_course_button, .course-buy-button, [data-course-buy], ' +
            '.masterstudy-buy-button__link, .btn-default[data-buy-course], ' +
            '.btn-default.buy-button, .stm_lms_buy_course, .buy-course'
        ).filter(function() {
            var buttonText = $(this).text().toLowerCase().trim();
            return (buttonText.includes('get') || buttonText.includes('buy') || 
                    buttonText.includes('purchase') || buttonText.includes('enroll') ||
                    buttonText.includes('add to cart'));
        });
        
        if (buyButtons.length === 0) {
            debugLog('No buy buttons found - user may be enrolled');
            
            // Additional check: if there are course-related elements but no buy buttons,
            // this likely means the user is enrolled
            var courseElements = $('.stm-lms-course, .course-content, .course-info, .stm_lms_course');
            if (courseElements.length > 0) {
                debugLog('Course elements found but no buy buttons - user is enrolled');
                $('body').addClass('custom-linking-user-enrolled');
                window.customFreeCourseUserEnrolled = true;
                return true;
            }
        }
        
        // Strategy 4: Check URL pattern for course access
        var currentUrl = window.location.href;
        if (currentUrl.includes('/lesson/') || currentUrl.includes('/curriculum/') || 
            currentUrl.match(/\/\d+\/$/) || currentUrl.includes('lesson_id')) {
            debugLog('URL indicates course access - user is enrolled');
            $('body').addClass('custom-linking-user-enrolled');
            window.customFreeCourseUserEnrolled = true;
            return true;
        }
        
        debugLog('User enrollment check: not enrolled');
        return false;
    }

    /**
     * Main initialization function
     */
    function initFreeCourseHandler() {
        debugLog('Initializing free course handler');
        
        // CRITICAL CHECK: Global flag to completely disable if user is enrolled
        if (window.customFreeCourseUserEnrolled === true) {
            debugLog('Global enrollment flag detected - completely disabling free course handler');
            return;
        }
        
        // CRITICAL CHECK: If user is already enrolled, don't intercept ANY buttons
        if (isUserEnrolledInCourse()) {
            debugLog('User appears to be enrolled in course - skipping free course handler to avoid interference');
            
            // Set global flag to prevent re-initialization
            window.customFreeCourseUserEnrolled = true;
            
            // Disable global functions
            window.customFreeCourseProcessCourse = function() {
                debugLog('Free course processing disabled - user is enrolled');
                return false;
            };
            
            window.customFreeCourseCheckIfFree = function() {
                debugLog('Free course check disabled - user is enrolled');
                return false;
            };
            
            return;
        }
        
        // STRICT FILTERING: Only target "Get Course" / "Buy" buttons, NEVER "Start Course" buttons
        // More restrictive selectors to avoid interference with post-enrollment buttons
        var $buyButtons = $(
            '#stm_lms_buy_button, .get_course_button, .course-buy-button, [data-course-buy], ' +
            '.masterstudy-buy-button__link, .btn-default[data-buy-course], ' +
            '.btn-default.buy-button, .stm_lms_mixed_button__purchase, .lms_purchase_button, ' +
            'a[href*="add-to-cart"], a[href*="buy-course"]'
        ).filter(function() {
            // Additional filtering to ensure we never catch "Start Course" buttons
            var $btn = $(this);
            var buttonText = $btn.text().toLowerCase().trim();
            var href = $btn.attr('href') || '';
            
            // Exclude any buttons that are clearly for starting/continuing courses
            if (buttonText.includes('start') || buttonText.includes('continue') || 
                buttonText.includes('resume') || buttonText.includes('begin') ||
                buttonText.includes('take course') || buttonText.includes('go to course') ||
                $btn.hasClass('start-course-btn') || $btn.hasClass('continue-course-btn') ||
                $btn.hasClass('stm-course-start-btn') || $btn.hasClass('course-start-button') ||
                $btn.attr('data-start-course') || href.includes('lesson') || href.includes('curriculum')) {
                debugLog('Filtering out start/continue button:', buttonText);
                return false;
            }
            
            // Only include buttons that are clearly for purchasing/getting courses
            return (buttonText.includes('get') || buttonText.includes('buy') || 
                    buttonText.includes('purchase') || buttonText.includes('enroll') ||
                    buttonText.includes('add to cart') || href.includes('add-to-cart') ||
                    $btn.hasClass('get_course_button') || $btn.hasClass('course-buy-button') ||
                    $btn.attr('data-course-buy') || $btn.attr('data-buy-course'));
        });        
        if ($buyButtons.length === 0) {
            debugLog('No valid course buy buttons found for free course handling');
            return;
        }
        
        // Remove any existing free course handlers to prevent duplicates
        $buyButtons.off('click.freeCourse');
        
        // Attach click handler with higher priority (runs before other handlers)
        // Use capturing phase to ensure this runs first
        $buyButtons.each(function() {
            this.addEventListener('click', function(e) {
                var $button = $(this);
                var buttonText = $button.text().toLowerCase().trim();
                
                // FINAL SAFETY CHECK: Never interfere with "Start Course" or similar buttons
                if (buttonText.includes('start') || buttonText.includes('continue') || 
                    buttonText.includes('resume') || buttonText.includes('begin') ||
                    buttonText.includes('take course') || buttonText.includes('go to course') ||
                    $button.hasClass('start-course-btn') || $button.hasClass('continue-course-btn') ||
                    $button.attr('data-start-course') || 
                    ($button.attr('href') && ($button.attr('href').includes('lesson') || $button.attr('href').includes('curriculum')))) {
                    debugLog('Final safety check: Ignoring start/continue course button:', buttonText);
                    return; // Allow normal LMS processing
                }
                
                var courseId = getCourseId();
                
                if (!courseId) {
                    debugLog('Could not determine course ID');
                    return; // Allow normal processing
                }

                // Check if this appears to be a free course
                if (isCourseFreeDOMCheck()) {
                    debugLog('Free course detected, intercepting click for button:', buttonText);
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    
                    processFreeCart(courseId);
                } else {
                    debugLog('Not a free course based on DOM check, allowing normal processing');
                }
            }, true); // Use capturing phase (runs before bubbling phase)
        });

        debugLog('Free course handler initialized, found', $buyButtons.length, 'buy buttons');
    }

    // Initialize when document is ready
    $(document).ready(function() {
        initFreeCourseHandler();
        
        // Periodic check for enrollment status changes
        setInterval(function() {
            if (window.customFreeCourseUserEnrolled !== true && isUserEnrolledInCourse()) {
                debugLog('User enrollment detected during periodic check - disabling handlers');
                window.customFreeCourseUserEnrolled = true;
                
                // Remove all event handlers
                $('.stm_lms_buy_button, .get_course_button, .course-buy-button, [data-course-buy]').off('click.freeCourse');
                
                // Disable global functions
                window.customFreeCourseProcessCourse = function() {
                    debugLog('Free course processing disabled - user enrolled');
                    return false;
                };
                
                window.customFreeCourseCheckIfFree = function() {
                    debugLog('Free course check disabled - user enrolled'); 
                    return false;
                };
            }
        }, 2000); // Check every 2 seconds
    });

    // Also initialize on AJAX complete (for dynamic content)
    $(document).ajaxComplete(function() {
        setTimeout(initFreeCourseHandler, 100);
    });
    
    // Initialize on page load complete (fallback)
    $(window).on('load', function() {
        setTimeout(initFreeCourseHandler, 200);
    });
    
    // Handle dynamic content loading (MutationObserver for button changes)
    if (typeof MutationObserver !== 'undefined') {
        var observer = new MutationObserver(function(mutations) {
            var shouldReinit = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (var i = 0; i < mutation.addedNodes.length; i++) {
                        var node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // Element node
                            // Check if new buttons were added
                            if ($(node).find('.stm_lms_buy_button, .get_course_button, [data-course-buy]').length > 0 ||
                                $(node).hasClass('stm_lms_buy_button') || $(node).hasClass('get_course_button')) {
                                shouldReinit = true;
                                break;
                            }
                        }
                    }
                }
            });
            
            if (shouldReinit) {
                debugLog('New course buttons detected - reinitializing handlers');
                setTimeout(initFreeCourseHandler, 100);
            }
        });
        
        // Start observing
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // Global function to allow main plugin to call free course processing
    window.customFreeCourseProcessCourse = function(courseId) {
        debugLog('Free course processing called from main plugin for course:', courseId);
        
        if (isCourseFreeDOMCheck()) {
            processFreeCart(courseId);
            return true; // Indicate we handled it
        }
        
        return false; // Indicate we didn't handle it
    };
    
    // Global function to check if course appears free from DOM
    window.customFreeCourseCheckIfFree = function() {
        return isCourseFreeDOMCheck();
    };
    
})(jQuery);
